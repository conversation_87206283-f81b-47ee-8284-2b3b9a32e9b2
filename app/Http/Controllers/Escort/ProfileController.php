<?php

namespace App\Http\Controllers\Escort;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Str;
use Illuminate\View\View;

class ProfileController extends Controller
{
    /**
     * Display the escort's profile form.
     */
    public function edit(Request $request): View
    {
        $user = $request->user();
        $user->load('escort');

        return view('escort.profile.edit', [
            'user' => $user,
            'escort' => $user->escort,
        ]);
    }

    /**
     * Update the escort's profile information.
     */
    public function update(Request $request)
    {
        $user = $request->user();
        $escort = $user->escort;

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'gender' => 'required|in:female,male,couple,gay,transsexual',
            'date_of_birth' => 'required|date|before:18 years ago',
            'ethnicity' => 'required|string',
            'hair_color' => 'required|string',
            'hair_length' => 'required|string',
            'bust_size' => 'nullable|string',
            'height_cm' => 'required|integer|min:140|max:220',
            'weight_kg' => 'required|integer|min:40|max:150',
            'build' => 'required|string',
            'looks' => 'required|string',
            'smoker' => 'boolean',
            'about' => 'required|string|min:50',
            'sports' => 'nullable|string|max:255',
            'hobbies' => 'nullable|string|max:255',
            'zodiac_sign' => 'nullable|string|max:255',
            'sexual_orientation' => 'nullable|string|max:255',
            'occupation' => 'nullable|string|max:255',
            'phone_number' => 'nullable|string|max:20',
            'whatsapp_number' => 'nullable|string|max:20',
            'show_phone_number' => 'boolean',
            'show_whatsapp' => 'boolean',
            'incall_available' => 'boolean',
            'outcall_available' => 'boolean',
        ]);

        // Generate slug if name changed
        if ($escort->name !== $validated['name']) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        $escort->update($validated);

        // Check if this is a new user (profile completion flow)
        // If they came from registration or have no photos, redirect to photos
        if ($escort->images()->count() === 0 || session('profile_completion_flow')) {
            // Clear the session flag
            session()->forget('profile_completion_flow');

            return Redirect::route('escort.photos')->with('success', 'Profile saved! Now add some photos to complete your profile.');
        }

        // For existing users updating their profile, stay on profile page
        return Redirect::route('profile.escort.edit')->with('status', 'profile-updated');
    }
}
