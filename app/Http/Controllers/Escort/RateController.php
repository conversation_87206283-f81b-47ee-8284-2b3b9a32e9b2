<?php

namespace App\Http\Controllers\Escort;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\EscortRate as Rate;

class RateController extends Controller
{
    /**
     * Display a listing of the escort's rates.
     */
    public function index()
    {
        $escort = Auth::user()->escort;
        $rates = $escort->rates;

        return view('escort.rates.index', compact('escort', 'rates'));
    }

    /**
     * Store a newly created rate in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'duration' => 'required|string|in:30min,1hour,2hours,3hours,6hours,12hours,24hours',
            'min_incall_price' => 'nullable|numeric|min:0',
            'max_incall_price' => 'nullable|numeric|min:0',
            'min_outcall_price' => 'nullable|numeric|min:0',
            'max_outcall_price' => 'nullable|numeric|min:0',
            'currency' => 'required|string|in:UGX',
            'description' => 'nullable|string|max:255',
        ]);

        // Validate that max prices are greater than or equal to min prices
        if ($request->min_incall_price && $request->max_incall_price && $request->min_incall_price > $request->max_incall_price) {
            return redirect()->back()->withErrors(['price_error' => 'Maximum incall price must be greater than or equal to minimum incall price.'])->withInput();
        }

        if ($request->min_outcall_price && $request->max_outcall_price && $request->min_outcall_price > $request->max_outcall_price) {
            return redirect()->back()->withErrors(['price_error' => 'Maximum outcall price must be greater than or equal to minimum outcall price.'])->withInput();
        }

        // Ensure at least one price range is provided
        if (empty($request->min_incall_price) && empty($request->max_incall_price) &&
            empty($request->min_outcall_price) && empty($request->max_outcall_price)) {
            return redirect()->back()->withErrors(['price_error' => 'At least one price range (incall or outcall) must be provided.'])->withInput();
        }

        // Ensure both min and max are provided if one is provided
        if (($request->min_incall_price && !$request->max_incall_price) || (!$request->min_incall_price && $request->max_incall_price)) {
            return redirect()->back()->withErrors(['price_error' => 'Both minimum and maximum incall prices must be provided.'])->withInput();
        }

        if (($request->min_outcall_price && !$request->max_outcall_price) || (!$request->min_outcall_price && $request->max_outcall_price)) {
            return redirect()->back()->withErrors(['price_error' => 'Both minimum and maximum outcall prices must be provided.'])->withInput();
        }

        $escort = Auth::user()->escort;

        $rate = Rate::create([
            'escort_id' => $escort->id,
            'duration' => $request->duration,
            'min_incall_price' => $request->min_incall_price,
            'max_incall_price' => $request->max_incall_price,
            'min_outcall_price' => $request->min_outcall_price,
            'max_outcall_price' => $request->max_outcall_price,
            'currency' => $request->currency,
            'description' => $request->description,
        ]);

        // Check if this is their first rate and they have no locations yet
        // Guide them to the next step in profile completion
        if ($escort->rates()->count() === 1 && $escort->locations()->count() === 0) {
            return redirect()->route('escort.locations')->with('success', 'Rate added successfully! Now set your service locations.');
        }

        return redirect()->route('escort.rates')->with('success', 'Rate added successfully.');
    }

    /**
     * Show the form for editing the specified rate.
     */
    public function edit($id)
    {
        $escort = Auth::user()->escort;
        $rate = Rate::where('id', $id)
            ->where('escort_id', $escort->id)
            ->firstOrFail();

        return view('escort.rates.edit', compact('escort', 'rate'));
    }

    /**
     * Update the specified rate in storage.
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'duration' => 'required|string|in:30min,1hour,2hours,3hours,6hours,12hours,24hours',
            'min_incall_price' => 'nullable|numeric|min:0',
            'max_incall_price' => 'nullable|numeric|min:0',
            'min_outcall_price' => 'nullable|numeric|min:0',
            'max_outcall_price' => 'nullable|numeric|min:0',
            'currency' => 'required|string|in:UGX',
            'description' => 'nullable|string|max:255',
        ]);

        // Validate that max prices are greater than or equal to min prices
        if ($request->min_incall_price && $request->max_incall_price && $request->min_incall_price > $request->max_incall_price) {
            return redirect()->back()->withErrors(['price_error' => 'Maximum incall price must be greater than or equal to minimum incall price.'])->withInput();
        }

        if ($request->min_outcall_price && $request->max_outcall_price && $request->min_outcall_price > $request->max_outcall_price) {
            return redirect()->back()->withErrors(['price_error' => 'Maximum outcall price must be greater than or equal to minimum outcall price.'])->withInput();
        }

        // Ensure at least one price range is provided
        if (empty($request->min_incall_price) && empty($request->max_incall_price) &&
            empty($request->min_outcall_price) && empty($request->max_outcall_price)) {
            return redirect()->back()->withErrors(['price_error' => 'At least one price range (incall or outcall) must be provided.'])->withInput();
        }

        // Ensure both min and max are provided if one is provided
        if (($request->min_incall_price && !$request->max_incall_price) || (!$request->min_incall_price && $request->max_incall_price)) {
            return redirect()->back()->withErrors(['price_error' => 'Both minimum and maximum incall prices must be provided.'])->withInput();
        }

        if (($request->min_outcall_price && !$request->max_outcall_price) || (!$request->min_outcall_price && $request->max_outcall_price)) {
            return redirect()->back()->withErrors(['price_error' => 'Both minimum and maximum outcall prices must be provided.'])->withInput();
        }

        $escort = Auth::user()->escort;
        $rate = Rate::where('id', $id)
            ->where('escort_id', $escort->id)
            ->firstOrFail();

        $rate->duration = $request->duration;
        $rate->min_incall_price = $request->min_incall_price;
        $rate->max_incall_price = $request->max_incall_price;
        $rate->min_outcall_price = $request->min_outcall_price;
        $rate->max_outcall_price = $request->max_outcall_price;
        $rate->currency = $request->currency;
        $rate->description = $request->description;
        $rate->save();

        return redirect()->route('escort.rates')->with('success', 'Rate updated successfully.');
    }

    /**
     * Remove the specified rate from storage.
     */
    public function destroy($id)
    {
        $escort = Auth::user()->escort;
        $rate = Rate::where('id', $id)
            ->where('escort_id', $escort->id)
            ->firstOrFail();

        $rate->delete();

        return redirect()->route('escort.rates')->with('success', 'Rate deleted successfully.');
    }
}
