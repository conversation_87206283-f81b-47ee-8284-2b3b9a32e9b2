<?php

namespace App\Http\Controllers\Agency;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\View\View;

class ProfileController extends Controller
{
    /**
     * Display the agency's profile form.
     */
    public function edit(Request $request): View
    {
        $user = $request->user();
        $user->load('agency');
        
        return view('agency.profile.edit', [
            'user' => $user,
            'agency' => $user->agency,
        ]);
    }

    /**
     * Update the agency's profile information.
     */
    public function update(Request $request)
    {
        $user = $request->user();
        $agency = $user->agency;
        
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string|min:50',
            'website' => 'nullable|url|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|max:255',
            'address' => 'required|string|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ]);
        
        // Generate slug if name changed
        if ($agency->name !== $validated['name']) {
            $validated['slug'] = Str::slug($validated['name']);
        }
        
        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            if ($agency->logo_path) {
                Storage::disk('public')->delete($agency->logo_path);
            }
            
            $path = $request->file('logo')->store('agencies', 'public');
            $validated['logo_path'] = $path;
        }
        
        $agency->update($validated);

        // Check if this is a new user (profile completion flow)
        // If they came from registration, redirect to locations setup
        if (session('profile_completion_flow')) {
            // Clear the session flag
            session()->forget('profile_completion_flow');

            return Redirect::route('agency.locations')->with('success', 'Profile saved! Now set up your service locations.');
        }

        // For existing users updating their profile, stay on profile page
        return Redirect::route('profile.agency.edit')->with('status', 'profile-updated');
    }
}
