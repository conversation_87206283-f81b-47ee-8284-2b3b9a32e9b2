<?php

namespace App\Http\Controllers\Agency;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use App\Models\User;
use App\Models\Escort;
use App\Models\UserProfile;

class EscortController extends Controller
{
    /**
     * Display a listing of the agency's escorts.
     */
    public function index()
    {
        $agency = Auth::user()->agency;
        // Order by verification status first, then by registration date (first come, first serve)
        $escorts = $agency->escorts()->with('user')
                          ->orderBy('is_verified', 'desc')
                          ->orderBy('created_at', 'asc')
                          ->paginate(10);
        
        return view('agency.escorts.index', compact('agency', 'escorts'));
    }

    /**
     * Show the form for creating a new escort.
     */
    public function create()
    {
        return view('agency.escorts.create');
    }

    /**
     * Store a newly created escort in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'username' => 'required|string|max:255|unique:users',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'name' => 'required|string|max:255',
            'gender' => 'required|in:female,male,couple,gay,transsexual',
            'date_of_birth' => 'required|date|before:18 years ago',
            'ethnicity' => 'required|string',
            'hair_color' => 'required|string',
            'hair_length' => 'required|string',
            'bust_size' => 'nullable|string',
            'height_cm' => 'required|integer|min:140|max:220',
            'weight_kg' => 'required|integer|min:40|max:150',
            'build' => 'required|string',
            'looks' => 'required|string',
            'about' => 'required|string|min:50',
        ]);
        
        // Create user
        $user = new User();
        $user->username = $request->username;
        $user->email = $request->email;
        $user->password = Hash::make($request->password);
        $user->user_type = 'escort';
        $user->is_active = true;
        $user->save();
        
        // Create user profile
        $profile = new UserProfile();
        $profile->user_id = $user->id;
        $profile->save();
        
        // Create escort
        $escort = new Escort();
        $escort->user_id = $user->id;
        $escort->agency_id = Auth::user()->agency->id;
        $escort->name = $request->name;
        $escort->gender = $request->gender;
        $escort->date_of_birth = $request->date_of_birth;
        $escort->ethnicity = $request->ethnicity;
        $escort->hair_color = $request->hair_color;
        $escort->hair_length = $request->hair_length;
        $escort->bust_size = $request->bust_size;
        $escort->height_cm = $request->height_cm;
        $escort->weight_kg = $request->weight_kg;
        $escort->build = $request->build;
        $escort->looks = $request->looks;
        $escort->about = $request->about;
        $escort->is_independent = false;
        $escort->slug = Str::slug($request->name);
        $escort->save();
        
        return redirect()->route('agency.escorts')->with('success', 'Escort created successfully.');
    }

    /**
     * Show the form for editing the specified escort.
     */
    public function edit($id)
    {
        $agency = Auth::user()->agency;
        $escort = Escort::where('id', $id)
            ->where('agency_id', $agency->id)
            ->firstOrFail();
        
        return view('agency.escorts.edit', compact('escort'));
    }

    /**
     * Update the specified escort in storage.
     */
    public function update(Request $request, $id)
    {
        $agency = Auth::user()->agency;
        $escort = Escort::where('id', $id)
            ->where('agency_id', $agency->id)
            ->firstOrFail();
        
        $request->validate([
            'name' => 'required|string|max:255',
            'gender' => 'required|in:female,male,couple,gay,transsexual',
            'date_of_birth' => 'required|date|before:18 years ago',
            'ethnicity' => 'required|string',
            'hair_color' => 'required|string',
            'hair_length' => 'required|string',
            'bust_size' => 'nullable|string',
            'height_cm' => 'required|integer|min:140|max:220',
            'weight_kg' => 'required|integer|min:40|max:150',
            'build' => 'required|string',
            'looks' => 'required|string',
            'about' => 'required|string|min:50',
            'is_featured' => 'boolean',
        ]);
        
        // Generate slug if name changed
        if ($escort->name !== $request->name) {
            $request->merge(['slug' => Str::slug($request->name)]);
        }
        
        $escort->update($request->all());
        
        return redirect()->route('agency.escorts')->with('success', 'Escort updated successfully.');
    }

    /**
     * Remove the specified escort from storage.
     */
    public function destroy($id)
    {
        $agency = Auth::user()->agency;
        $escort = Escort::where('id', $id)
            ->where('agency_id', $agency->id)
            ->firstOrFail();
        
        // Remove agency association but don't delete the escort
        $escort->agency_id = null;
        $escort->is_independent = true;
        $escort->save();
        
        return redirect()->route('agency.escorts')->with('success', 'Escort removed from agency.');
    }
}
