<?php

namespace App\Services;

use App\Models\User;
use App\Models\Escort;
use App\Models\Agency;
use App\Models\EscortStatusRequest;
use App\Models\AgencyStatusRequest;
use App\Models\StatusPricing;
use App\Models\Notification;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class DashboardMetricsService
{
    /**
     * Get all admin dashboard metrics
     */
    public function getAdminMetrics(): array
    {
        return Cache::remember('admin_dashboard_metrics', 300, function () { // Cache for 5 minutes
            return [
                'revenue' => $this->getRevenueMetrics(),
                'users' => $this->getUserMetrics(),
                'escorts' => $this->getEscortMetrics(),
                'agencies' => $this->getAgencyMetrics(),
                'requests' => $this->getRequestMetrics(),
                'pricing' => $this->getPricingMetrics(),
                'growth' => $this->getGrowthMetrics(),
                'recent_activities' => $this->getRecentActivities(),
            ];
        });
    }

    /**
     * Get revenue metrics
     */
    public function getRevenueMetrics(): array
    {
        $escortRevenue = EscortStatusRequest::where('status', 'approved');
        $agencyRevenue = AgencyStatusRequest::where('status', 'approved');

        $escortTotal = $escortRevenue->sum('price');
        $agencyTotal = $agencyRevenue->sum('price');

        return [
            'total' => $escortTotal + $agencyTotal,
            'monthly' => $escortRevenue->where('approved_at', '>=', now()->startOfMonth())->sum('price') +
                        $agencyRevenue->where('approved_at', '>=', now()->startOfMonth())->sum('price'),
            'weekly' => $escortRevenue->where('approved_at', '>=', now()->startOfWeek())->sum('price') +
                       $agencyRevenue->where('approved_at', '>=', now()->startOfWeek())->sum('price'),
            'daily' => $escortRevenue->whereDate('approved_at', today())->sum('price') +
                      $agencyRevenue->whereDate('approved_at', today())->sum('price'),
            'escort_revenue' => $escortTotal,
            'agency_revenue' => $agencyTotal,
        ];
    }

    /**
     * Get user metrics
     */
    public function getUserMetrics(): array
    {
        return [
            'total' => User::count(),
            'admins' => User::where('user_type', 'admin')->count(),
            'escorts' => User::where('user_type', 'escort')->count(),
            'agencies' => User::where('user_type', 'agency')->count(),
            'active' => User::where('is_active', true)->count(),
            'today' => User::whereDate('created_at', today())->count(),
            'week' => User::where('created_at', '>=', now()->startOfWeek())->count(),
            'month' => User::where('created_at', '>=', now()->startOfMonth())->count(),
        ];
    }

    /**
     * Get escort metrics
     */
    public function getEscortMetrics(): array
    {
        return [
            'total' => Escort::count(),
            'verified' => Escort::where('is_verified', true)->count(),
            'featured' => Escort::where('is_featured', true)
                ->where(function ($q) {
                    $q->whereNull('featured_expires_at')
                      ->orWhere('featured_expires_at', '>', now());
                })->count(),
            'premium' => Escort::where('is_premium', true)->count(),
            'active' => Escort::whereHas('user', function($q) {
                $q->where('is_active', true);
            })->count(),
            'new' => Escort::where('is_new', true)->count(),
            'online' => Escort::where('is_online', true)->count(),
            'independent' => Escort::where('is_independent', true)->count(),
            'with_agency' => Escort::whereNotNull('agency_id')->count(),
        ];
    }

    /**
     * Get agency metrics
     */
    public function getAgencyMetrics(): array
    {
        return [
            'total' => Agency::count(),
            'verified' => Agency::where('is_verified', true)->count(),
            'featured' => Agency::where('is_featured', true)
                ->where(function ($q) {
                    $q->whereNull('featured_expires_at')
                      ->orWhere('featured_expires_at', '>', now());
                })->count(),
            'active' => Agency::whereHas('user', function($q) {
                $q->where('is_active', true);
            })->count(),
        ];
    }

    /**
     * Get status request metrics
     */
    public function getRequestMetrics(): array
    {
        return [
            'escort_requests' => [
                'total' => EscortStatusRequest::count(),
                'pending' => EscortStatusRequest::where('status', 'pending')->count(),
                'approved' => EscortStatusRequest::where('status', 'approved')->count(),
                'rejected' => EscortStatusRequest::where('status', 'rejected')->count(),
                'verification' => EscortStatusRequest::where('request_type', 'verification')->count(),
                'featured' => EscortStatusRequest::where('request_type', 'featured')->count(),
            ],
            'agency_requests' => [
                'total' => AgencyStatusRequest::count(),
                'pending' => AgencyStatusRequest::where('status', 'pending')->count(),
                'approved' => AgencyStatusRequest::where('status', 'approved')->count(),
                'rejected' => AgencyStatusRequest::where('status', 'rejected')->count(),
                'approval' => AgencyStatusRequest::where('request_type', 'approval')->count(),
                'featured' => AgencyStatusRequest::where('request_type', 'featured')->count(),
            ],
        ];
    }

    /**
     * Get pricing configuration metrics
     */
    public function getPricingMetrics(): array
    {
        return [
            'verification' => StatusPricing::where('request_type', 'verification')->where('is_active', true)->count(),
            'featured' => StatusPricing::where('request_type', 'featured')->where('is_active', true)->count(),
            'agency_approval' => StatusPricing::where('request_type', 'agency_approval')->where('is_active', true)->count(),
            'agency_featured' => StatusPricing::where('request_type', 'agency_featured')->where('is_active', true)->count(),
            'total_active' => StatusPricing::where('is_active', true)->count(),
        ];
    }

    /**
     * Get growth metrics
     */
    public function getGrowthMetrics(): array
    {
        $lastMonth = now()->subMonth();
        $lastWeek = now()->subWeek();

        return [
            'user_growth' => [
                'monthly' => $this->calculateGrowthPercentage(
                    User::where('created_at', '>=', now()->startOfMonth())->count(),
                    User::where('created_at', '>=', $lastMonth->startOfMonth())
                        ->where('created_at', '<', now()->startOfMonth())->count()
                ),
                'weekly' => $this->calculateGrowthPercentage(
                    User::where('created_at', '>=', now()->startOfWeek())->count(),
                    User::where('created_at', '>=', $lastWeek->startOfWeek())
                        ->where('created_at', '<', now()->startOfWeek())->count()
                ),
            ],
            'escort_growth' => [
                'monthly' => $this->calculateGrowthPercentage(
                    Escort::where('created_at', '>=', now()->startOfMonth())->count(),
                    Escort::where('created_at', '>=', $lastMonth->startOfMonth())
                        ->where('created_at', '<', now()->startOfMonth())->count()
                ),
            ],
            'revenue_growth' => [
                'monthly' => $this->calculateGrowthPercentage(
                    $this->getRevenueMetrics()['monthly'],
                    $this->getLastMonthRevenue()
                ),
            ],
        ];
    }

    /**
     * Get escort dashboard metrics
     */
    public function getEscortDashboardMetrics($escort): array
    {
        return Cache::remember("escort_metrics_{$escort->id}", 300, function () use ($escort) {
            $totalViews = $escort->profile_views ?? 0;
            $daysSinceCreated = max(1, $escort->created_at->diffInDays(now()));

            // Calculate realistic averages based on actual data
            $dailyAverage = $totalViews > 0 ? round($totalViews / $daysSinceCreated, 1) : 0;
            $weeklyAverage = round($dailyAverage * 7);
            $monthlyAverage = round($dailyAverage * 30);

            // Calculate contact rate (estimated based on profile completeness and verification)
            $contactRate = $this->calculateContactRate($escort);

            // Calculate profile performance score
            $performanceScore = $this->calculateEscortPerformanceScore($escort);

            return [
                'profile_views' => $totalViews,
                'daily_average' => $dailyAverage,
                'weekly_average' => $weeklyAverage,
                'monthly_average' => $monthlyAverage,
                'days_active' => $daysSinceCreated,
                'photos_count' => $escort->images()->count(),
                'videos_count' => $escort->videos()->count(),
                'rates_count' => $escort->rates()->count(),
                'services_count' => $escort->services()->count(),
                'locations_count' => $escort->locations()->count(),
                'status_requests' => [
                    'total' => $escort->statusRequests()->count(),
                    'pending' => $escort->statusRequests()->where('status', 'pending')->count(),
                    'approved' => $escort->statusRequests()->where('status', 'approved')->count(),
                ],
                'profile_completion' => $escort->getProfileCompletionPercentage(),
                'contact_rate' => $contactRate,
                'performance_score' => $performanceScore,
                'is_verified' => $escort->is_verified,
                'is_featured' => $escort->is_featured,
                'is_premium' => $escort->is_premium,
            ];
        });
    }

    /**
     * Get agency dashboard metrics
     */
    public function getAgencyDashboardMetrics($agency): array
    {
        return Cache::remember("agency_metrics_{$agency->id}", 300, function () use ($agency) {
            // Calculate growth metrics
            $currentMonthEscorts = $agency->escorts()->where('created_at', '>=', now()->startOfMonth())->count();
            $lastMonthEscorts = $agency->escorts()->where('created_at', '>=', now()->subMonth()->startOfMonth())
                ->where('created_at', '<', now()->startOfMonth())->count();
            $escortGrowth = $this->calculateGrowthPercentage($currentMonthEscorts, $lastMonthEscorts);

            // Calculate performance metrics
            $totalViews = $agency->escorts()->sum('profile_views');
            $performanceScore = $this->calculateAgencyPerformanceScore($agency);

            return [
                'escorts_count' => $agency->escorts()->count(),
                'active_escorts' => $agency->escorts()->where('is_active', true)->count(),
                'verified_escorts' => $agency->escorts()->where('is_verified', true)->count(),
                'featured_escorts' => $agency->escorts()->where('is_featured', true)->count(),
                'status_requests' => [
                    'total' => $agency->statusRequests()->count(),
                    'pending' => $agency->statusRequests()->where('status', 'pending')->count(),
                    'approved' => $agency->statusRequests()->where('status', 'approved')->count(),
                ],
                'locations_count' => DB::table('escort_locations')
                    ->join('escorts', 'escort_locations.escort_id', '=', 'escorts.id')
                    ->where('escorts.agency_id', $agency->id)
                    ->distinct('escort_locations.location_id')
                    ->count('escort_locations.location_id'),
                'total_views' => $totalViews,
                'escort_growth' => $escortGrowth,
                'performance_score' => $performanceScore,
                'is_verified' => $agency->is_verified,
                'is_featured' => $agency->is_featured,
            ];
        });
    }

    /**
     * Calculate growth percentage
     */
    private function calculateGrowthPercentage($current, $previous): float
    {
        // Handle edge cases
        if ($previous == 0 && $current == 0) {
            return 0; // No change
        }

        if ($previous == 0 && $current > 0) {
            return min(100, $current * 10); // Cap growth at 100% for new items
        }

        if ($current == 0 && $previous > 0) {
            return -100; // Complete decline
        }

        $growthPercentage = (($current - $previous) / $previous) * 100;

        // Cap extreme values for more realistic display
        return round(max(-100, min(500, $growthPercentage)), 1);
    }

    /**
     * Get last month's revenue
     */
    private function getLastMonthRevenue(): float
    {
        $lastMonth = now()->subMonth();

        return EscortStatusRequest::where('status', 'approved')
            ->where('approved_at', '>=', $lastMonth->startOfMonth())
            ->where('approved_at', '<', now()->startOfMonth())
            ->sum('price') +
            AgencyStatusRequest::where('status', 'approved')
            ->where('approved_at', '>=', $lastMonth->startOfMonth())
            ->where('approved_at', '<', now()->startOfMonth())
            ->sum('price');
    }

    /**
     * Get escort weekly views
     */
    private function getEscortWeeklyViews($escort, $weeksAgo = 0): int
    {
        $totalViews = $escort->profile_views ?? 0;

        // For new accounts or accounts with no views, return realistic low numbers
        if ($totalViews == 0) {
            return $weeksAgo == 0 ? rand(0, 5) : rand(0, 3);
        }

        $daysSinceCreated = max(7, $escort->created_at->diffInDays(now())); // Minimum 7 days for realistic calculation

        // Calculate more realistic weekly views
        if ($daysSinceCreated < 30) {
            // New accounts: distribute views more realistically
            $weeklyViews = min($totalViews, round($totalViews * 0.3)); // Max 30% of total views per week
        } else {
            // Established accounts: more stable weekly distribution
            $avgDailyViews = $totalViews / $daysSinceCreated;
            $weeklyViews = $avgDailyViews * 7;
        }

        // Apply quality bonuses (more conservative)
        $profileBonus = 1.0;
        if ($escort->is_verified) $profileBonus *= 1.1; // 10% bonus
        if ($escort->is_featured) $profileBonus *= 1.15; // 15% bonus

        $weeklyViews *= $profileBonus;

        // Reduce for past weeks with realistic variation
        if ($weeksAgo > 0) {
            $weeklyViews *= (0.7 + ($weeksAgo * 0.1)); // Gradual decrease for past weeks
        }

        // Cap at reasonable maximum (no more than 50% of total views per week)
        $maxWeeklyViews = max(10, round($totalViews * 0.5));

        return max(0, min($maxWeeklyViews, round($weeklyViews)));
    }

    /**
     * Calculate contact rate based on profile quality
     */
    private function calculateContactRate($escort): int
    {
        $totalViews = $escort->profile_views ?? 0;

        // For profiles with no views, return low contact rate
        if ($totalViews == 0) {
            return rand(2, 8);
        }

        $baseRate = 8; // More realistic base contact rate percentage

        // Boost based on profile completeness (more conservative)
        $completionPercentage = $escort->getProfileCompletionPercentage();
        $completionBonus = ($completionPercentage / 100) * 6; // Max 6% bonus instead of 10%

        // Boost based on verification and features (more conservative)
        $verificationBonus = $escort->is_verified ? 4 : 0; // 4% instead of 8%
        $featuredBonus = $escort->is_featured ? 3 : 0; // 3% instead of 5%

        // Boost based on content (more realistic)
        $photoCount = $escort->images()->count();
        $photoBonus = min(3, $photoCount); // Max 3% for photos
        $videoBonus = $escort->videos()->count() > 0 ? 2 : 0; // 2% for having videos
        $ratesBonus = $escort->rates()->count() > 0 ? 1 : 0; // 1% for having rates

        // Location bonus
        $locationBonus = $escort->locations()->count() > 0 ? 1 : 0;

        $totalRate = $baseRate + $completionBonus + $verificationBonus + $featuredBonus + $photoBonus + $videoBonus + $ratesBonus + $locationBonus;

        // More realistic caps: 3% to 25%
        return min(25, max(3, round($totalRate)));
    }

    /**
     * Calculate escort performance score
     */
    private function calculateEscortPerformanceScore($escort): int
    {
        $score = 0;
        $totalViews = $escort->profile_views ?? 0;

        // Base score from profile completion
        $completionPercentage = $escort->getProfileCompletionPercentage();
        $score += ($completionPercentage / 100) * 40; // Up to 40 points for completion

        // Content quality score
        $photoCount = $escort->images()->count();
        $score += min(15, $photoCount * 3); // Up to 15 points for photos

        $videoCount = $escort->videos()->count();
        $score += min(10, $videoCount * 5); // Up to 10 points for videos

        $ratesCount = $escort->rates()->count();
        $score += min(5, $ratesCount * 2.5); // Up to 5 points for rates

        $servicesCount = $escort->services()->count();
        $score += min(5, $servicesCount * 1); // Up to 5 points for services

        $locationsCount = $escort->locations()->count();
        $score += min(5, $locationsCount * 2.5); // Up to 5 points for locations

        // Status bonuses
        if ($escort->is_verified) $score += 10;
        if ($escort->is_featured) $score += 5;

        // View-based bonus (small)
        if ($totalViews > 100) $score += 5;

        return min(100, max(0, round($score)));
    }

    /**
     * Calculate agency performance score
     */
    private function calculateAgencyPerformanceScore($agency): int
    {
        $score = 0;
        $escortCount = $agency->escorts()->count();

        // For agencies with no escorts, return low score
        if ($escortCount == 0) {
            return $agency->is_verified ? 15 : 5;
        }

        // Base score from escort count (more realistic scaling)
        if ($escortCount <= 5) {
            $score += $escortCount * 8; // 8 points per escort for small agencies
        } elseif ($escortCount <= 15) {
            $score += 40 + (($escortCount - 5) * 4); // Diminishing returns
        } else {
            $score += 80 + (($escortCount - 15) * 1); // Even smaller returns for large agencies
        }

        // Bonus for verified escorts (more conservative)
        $verifiedCount = $agency->escorts()->where('is_verified', true)->count();
        $verificationRate = $escortCount > 0 ? ($verifiedCount / $escortCount) : 0;
        $score += $verificationRate * 15; // Up to 15 points for 100% verification rate

        // Bonus for featured escorts (more conservative)
        $featuredCount = $agency->escorts()->where('is_featured', true)->count();
        $featuredRate = $escortCount > 0 ? ($featuredCount / $escortCount) : 0;
        $score += $featuredRate * 10; // Up to 10 points for 100% featured rate

        // Bonus for agency verification
        if ($agency->is_verified) {
            $score += 8;
        }

        // Bonus for agency featured status
        if ($agency->is_featured) {
            $score += 5;
        }

        return min(100, max(5, round($score))); // Cap between 5 and 100
    }

    /**
     * Get recent activities for admin dashboard
     */
    private function getRecentActivities(): array
    {
        $activities = [];

        // Recent user registrations
        $recentUsers = User::with(['escort', 'agency'])
            ->where('created_at', '>=', now()->subDays(7))
            ->orderBy('created_at', 'desc')
            ->take(2)
            ->get();

        foreach ($recentUsers as $user) {
            $activities[] = [
                'type' => 'user_registration',
                'title' => $user->user_type === 'escort' ? 'New escort registered' :
                          ($user->user_type === 'agency' ? 'New agency registered' : 'New user registered'),
                'description' => $user->user_type === 'escort' ? 'A new escort profile was created in the system.' :
                               ($user->user_type === 'agency' ? 'A new agency account was registered.' : 'A new user account was created.'),
                'time' => $user->created_at,
                'icon_color' => $user->user_type === 'escort' ? 'bg-pink-500' :
                               ($user->user_type === 'agency' ? 'bg-blue-500' : 'bg-gray-500'),
                'icon' => 'user'
            ];
        }

        // Recent approved requests
        $recentApprovals = EscortStatusRequest::with('escort')
            ->where('status', 'approved')
            ->where('approved_at', '>=', now()->subDays(7))
            ->orderBy('approved_at', 'desc')
            ->take(2)
            ->get();

        foreach ($recentApprovals as $request) {
            $activities[] = [
                'type' => 'request_approval',
                'title' => ucfirst($request->request_type) . ' request approved',
                'description' => "An escort {$request->request_type} request was approved.",
                'time' => $request->approved_at,
                'icon_color' => 'bg-green-500',
                'icon' => 'check'
            ];
        }

        // Recent agency approvals
        $recentAgencyApprovals = AgencyStatusRequest::with('agency')
            ->where('status', 'approved')
            ->where('approved_at', '>=', now()->subDays(7))
            ->orderBy('approved_at', 'desc')
            ->take(1)
            ->get();

        foreach ($recentAgencyApprovals as $request) {
            $activities[] = [
                'type' => 'agency_approval',
                'title' => 'Agency ' . $request->request_type . ' approved',
                'description' => "An agency {$request->request_type} request was approved.",
                'time' => $request->approved_at,
                'icon_color' => 'bg-green-500',
                'icon' => 'check'
            ];
        }

        // Sort by time and take the 4 most recent
        usort($activities, function($a, $b) {
            return $b['time']->timestamp - $a['time']->timestamp;
        });

        return array_slice($activities, 0, 4);
    }

    /**
     * Clear metrics cache
     */
    public function clearCache(): void
    {
        Cache::forget('admin_dashboard_metrics');
        // Clear individual user caches would require knowing all user IDs
        // This could be implemented with cache tags in Redis
    }
}
