<?php

namespace App\Services;

use App\Models\User;
use App\Models\Escort;
use App\Models\Agency;
use App\Models\EscortStatusRequest;
use App\Models\AgencyStatusRequest;
use App\Models\StatusPricing;
use App\Models\Notification;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class DashboardMetricsService
{
    /**
     * Get all admin dashboard metrics
     */
    public function getAdminMetrics(): array
    {
        return Cache::remember('admin_dashboard_metrics', 300, function () { // Cache for 5 minutes
            return [
                'revenue' => $this->getRevenueMetrics(),
                'users' => $this->getUserMetrics(),
                'escorts' => $this->getEscortMetrics(),
                'agencies' => $this->getAgencyMetrics(),
                'requests' => $this->getRequestMetrics(),
                'pricing' => $this->getPricingMetrics(),
                'growth' => $this->getGrowthMetrics(),
            ];
        });
    }

    /**
     * Get revenue metrics
     */
    public function getRevenueMetrics(): array
    {
        $escortRevenue = EscortStatusRequest::where('status', 'approved');
        $agencyRevenue = AgencyStatusRequest::where('status', 'approved');

        $escortTotal = $escortRevenue->sum('price');
        $agencyTotal = $agencyRevenue->sum('price');

        return [
            'total' => $escortTotal + $agencyTotal,
            'monthly' => $escortRevenue->where('approved_at', '>=', now()->startOfMonth())->sum('price') +
                        $agencyRevenue->where('approved_at', '>=', now()->startOfMonth())->sum('price'),
            'weekly' => $escortRevenue->where('approved_at', '>=', now()->startOfWeek())->sum('price') +
                       $agencyRevenue->where('approved_at', '>=', now()->startOfWeek())->sum('price'),
            'daily' => $escortRevenue->whereDate('approved_at', today())->sum('price') +
                      $agencyRevenue->whereDate('approved_at', today())->sum('price'),
            'escort_revenue' => $escortTotal,
            'agency_revenue' => $agencyTotal,
        ];
    }

    /**
     * Get user metrics
     */
    public function getUserMetrics(): array
    {
        return [
            'total' => User::count(),
            'admins' => User::where('user_type', 'admin')->count(),
            'escorts' => User::where('user_type', 'escort')->count(),
            'agencies' => User::where('user_type', 'agency')->count(),
            'active' => User::where('is_active', true)->count(),
            'today' => User::whereDate('created_at', today())->count(),
            'week' => User::where('created_at', '>=', now()->startOfWeek())->count(),
            'month' => User::where('created_at', '>=', now()->startOfMonth())->count(),
        ];
    }

    /**
     * Get escort metrics
     */
    public function getEscortMetrics(): array
    {
        return [
            'total' => Escort::count(),
            'verified' => Escort::where('is_verified', true)->count(),
            'featured' => Escort::where('is_featured', true)
                ->where(function ($q) {
                    $q->whereNull('featured_expires_at')
                      ->orWhere('featured_expires_at', '>', now());
                })->count(),
            'premium' => Escort::where('is_premium', true)->count(),
            'active' => Escort::whereHas('user', function($q) {
                $q->where('is_active', true);
            })->count(),
            'new' => Escort::where('is_new', true)->count(),
            'online' => Escort::where('is_online', true)->count(),
            'independent' => Escort::where('is_independent', true)->count(),
            'with_agency' => Escort::whereNotNull('agency_id')->count(),
        ];
    }

    /**
     * Get agency metrics
     */
    public function getAgencyMetrics(): array
    {
        return [
            'total' => Agency::count(),
            'verified' => Agency::where('is_verified', true)->count(),
            'featured' => Agency::where('is_featured', true)
                ->where(function ($q) {
                    $q->whereNull('featured_expires_at')
                      ->orWhere('featured_expires_at', '>', now());
                })->count(),
            'active' => Agency::whereHas('user', function($q) {
                $q->where('is_active', true);
            })->count(),
        ];
    }

    /**
     * Get status request metrics
     */
    public function getRequestMetrics(): array
    {
        return [
            'escort_requests' => [
                'total' => EscortStatusRequest::count(),
                'pending' => EscortStatusRequest::where('status', 'pending')->count(),
                'approved' => EscortStatusRequest::where('status', 'approved')->count(),
                'rejected' => EscortStatusRequest::where('status', 'rejected')->count(),
                'verification' => EscortStatusRequest::where('request_type', 'verification')->count(),
                'featured' => EscortStatusRequest::where('request_type', 'featured')->count(),
            ],
            'agency_requests' => [
                'total' => AgencyStatusRequest::count(),
                'pending' => AgencyStatusRequest::where('status', 'pending')->count(),
                'approved' => AgencyStatusRequest::where('status', 'approved')->count(),
                'rejected' => AgencyStatusRequest::where('status', 'rejected')->count(),
                'approval' => AgencyStatusRequest::where('request_type', 'approval')->count(),
                'featured' => AgencyStatusRequest::where('request_type', 'featured')->count(),
            ],
        ];
    }

    /**
     * Get pricing configuration metrics
     */
    public function getPricingMetrics(): array
    {
        return [
            'verification' => StatusPricing::where('request_type', 'verification')->where('is_active', true)->count(),
            'featured' => StatusPricing::where('request_type', 'featured')->where('is_active', true)->count(),
            'agency_approval' => StatusPricing::where('request_type', 'agency_approval')->where('is_active', true)->count(),
            'agency_featured' => StatusPricing::where('request_type', 'agency_featured')->where('is_active', true)->count(),
            'total_active' => StatusPricing::where('is_active', true)->count(),
        ];
    }

    /**
     * Get growth metrics
     */
    public function getGrowthMetrics(): array
    {
        $lastMonth = now()->subMonth();
        $lastWeek = now()->subWeek();

        return [
            'user_growth' => [
                'monthly' => $this->calculateGrowthPercentage(
                    User::where('created_at', '>=', now()->startOfMonth())->count(),
                    User::where('created_at', '>=', $lastMonth->startOfMonth())
                        ->where('created_at', '<', now()->startOfMonth())->count()
                ),
                'weekly' => $this->calculateGrowthPercentage(
                    User::where('created_at', '>=', now()->startOfWeek())->count(),
                    User::where('created_at', '>=', $lastWeek->startOfWeek())
                        ->where('created_at', '<', now()->startOfWeek())->count()
                ),
            ],
            'escort_growth' => [
                'monthly' => $this->calculateGrowthPercentage(
                    Escort::where('created_at', '>=', now()->startOfMonth())->count(),
                    Escort::where('created_at', '>=', $lastMonth->startOfMonth())
                        ->where('created_at', '<', now()->startOfMonth())->count()
                ),
            ],
            'revenue_growth' => [
                'monthly' => $this->calculateGrowthPercentage(
                    $this->getRevenueMetrics()['monthly'],
                    $this->getLastMonthRevenue()
                ),
            ],
        ];
    }

    /**
     * Get escort dashboard metrics
     */
    public function getEscortDashboardMetrics($escort): array
    {
        return Cache::remember("escort_metrics_{$escort->id}", 300, function () use ($escort) {
            // Calculate weekly views (current week vs last week)
            $currentWeekViews = $this->getEscortWeeklyViews($escort, 0);
            $lastWeekViews = $this->getEscortWeeklyViews($escort, 1);
            $weeklyTrend = $this->calculateGrowthPercentage($currentWeekViews, $lastWeekViews);

            // Calculate contact rate (estimated based on profile completeness and verification)
            $contactRate = $this->calculateContactRate($escort);

            return [
                'profile_views' => $escort->profile_views ?? 0,
                'photos_count' => $escort->images()->count(),
                'videos_count' => $escort->videos()->count(),
                'rates_count' => $escort->rates()->count(),
                'services_count' => $escort->services()->count(),
                'locations_count' => $escort->locations()->count(),
                'status_requests' => [
                    'total' => $escort->statusRequests()->count(),
                    'pending' => $escort->statusRequests()->where('status', 'pending')->count(),
                    'approved' => $escort->statusRequests()->where('status', 'approved')->count(),
                ],
                'profile_completion' => $escort->getProfileCompletionPercentage(),
                'weekly_views' => $currentWeekViews,
                'weekly_trend' => $weeklyTrend,
                'contact_rate' => $contactRate,
                'is_verified' => $escort->is_verified,
                'is_featured' => $escort->is_featured,
                'is_premium' => $escort->is_premium,
            ];
        });
    }

    /**
     * Get agency dashboard metrics
     */
    public function getAgencyDashboardMetrics($agency): array
    {
        return Cache::remember("agency_metrics_{$agency->id}", 300, function () use ($agency) {
            // Calculate growth metrics
            $currentMonthEscorts = $agency->escorts()->where('created_at', '>=', now()->startOfMonth())->count();
            $lastMonthEscorts = $agency->escorts()->where('created_at', '>=', now()->subMonth()->startOfMonth())
                ->where('created_at', '<', now()->startOfMonth())->count();
            $escortGrowth = $this->calculateGrowthPercentage($currentMonthEscorts, $lastMonthEscorts);

            // Calculate performance metrics
            $totalViews = $agency->escorts()->sum('profile_views');
            $performanceScore = $this->calculateAgencyPerformanceScore($agency);

            return [
                'escorts_count' => $agency->escorts()->count(),
                'active_escorts' => $agency->escorts()->where('is_active', true)->count(),
                'verified_escorts' => $agency->escorts()->where('is_verified', true)->count(),
                'featured_escorts' => $agency->escorts()->where('is_featured', true)->count(),
                'status_requests' => [
                    'total' => $agency->statusRequests()->count(),
                    'pending' => $agency->statusRequests()->where('status', 'pending')->count(),
                    'approved' => $agency->statusRequests()->where('status', 'approved')->count(),
                ],
                'locations_count' => DB::table('escort_locations')
                    ->join('escorts', 'escort_locations.escort_id', '=', 'escorts.id')
                    ->where('escorts.agency_id', $agency->id)
                    ->distinct('escort_locations.location_id')
                    ->count('escort_locations.location_id'),
                'total_views' => $totalViews,
                'escort_growth' => $escortGrowth,
                'performance_score' => $performanceScore,
                'is_verified' => $agency->is_verified,
                'is_featured' => $agency->is_featured,
            ];
        });
    }

    /**
     * Calculate growth percentage
     */
    private function calculateGrowthPercentage($current, $previous): float
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return round((($current - $previous) / $previous) * 100, 1);
    }

    /**
     * Get last month's revenue
     */
    private function getLastMonthRevenue(): float
    {
        $lastMonth = now()->subMonth();

        return EscortStatusRequest::where('status', 'approved')
            ->where('approved_at', '>=', $lastMonth->startOfMonth())
            ->where('approved_at', '<', now()->startOfMonth())
            ->sum('price') +
            AgencyStatusRequest::where('status', 'approved')
            ->where('approved_at', '>=', $lastMonth->startOfMonth())
            ->where('approved_at', '<', now()->startOfMonth())
            ->sum('price');
    }

    /**
     * Get escort weekly views
     */
    private function getEscortWeeklyViews($escort, $weeksAgo = 0): int
    {
        // Since we don't have detailed view tracking, estimate based on total views and time
        $totalViews = $escort->profile_views ?? 0;
        $daysSinceCreated = max(1, $escort->created_at->diffInDays(now()));
        $avgDailyViews = $totalViews / $daysSinceCreated;

        // Estimate weekly views (with some variation for recent weeks)
        $baseWeeklyViews = $avgDailyViews * 7;

        // Add some realistic variation based on profile quality
        $profileBonus = ($escort->is_verified ? 1.2 : 1.0) * ($escort->is_featured ? 1.3 : 1.0);
        $weeklyViews = $baseWeeklyViews * $profileBonus;

        // Reduce for past weeks
        if ($weeksAgo > 0) {
            $weeklyViews *= (0.8 + (rand(0, 40) / 100)); // 80-120% of current week
        }

        return max(0, round($weeklyViews));
    }

    /**
     * Calculate contact rate based on profile quality
     */
    private function calculateContactRate($escort): int
    {
        $baseRate = 15; // Base contact rate percentage

        // Boost based on profile completeness
        $completionBonus = ($escort->getProfileCompletionPercentage() / 100) * 10;

        // Boost based on verification and features
        $verificationBonus = $escort->is_verified ? 8 : 0;
        $featuredBonus = $escort->is_featured ? 5 : 0;

        // Boost based on content
        $photoBonus = min(5, $escort->images()->count());
        $videoBonus = $escort->videos()->count() > 0 ? 3 : 0;
        $ratesBonus = $escort->rates()->count() > 0 ? 2 : 0;

        $totalRate = $baseRate + $completionBonus + $verificationBonus + $featuredBonus + $photoBonus + $videoBonus + $ratesBonus;

        return min(45, max(5, round($totalRate))); // Cap between 5% and 45%
    }

    /**
     * Calculate agency performance score
     */
    private function calculateAgencyPerformanceScore($agency): int
    {
        $score = 0;

        // Base score from escort count
        $escortCount = $agency->escorts()->count();
        $score += min(30, $escortCount * 3);

        // Bonus for verified escorts
        $verifiedCount = $agency->escorts()->where('is_verified', true)->count();
        $score += $verifiedCount * 5;

        // Bonus for featured escorts
        $featuredCount = $agency->escorts()->where('is_featured', true)->count();
        $score += $featuredCount * 3;

        // Bonus for agency verification
        if ($agency->is_verified) {
            $score += 15;
        }

        // Bonus for agency featured status
        if ($agency->is_featured) {
            $score += 10;
        }

        return min(100, max(0, $score)); // Cap between 0 and 100
    }

    /**
     * Clear metrics cache
     */
    public function clearCache(): void
    {
        Cache::forget('admin_dashboard_metrics');
        // Clear individual user caches would require knowing all user IDs
        // This could be implemented with cache tags in Redis
    }
}
