<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Escort extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'agency_id',
        'name',
        'gender',
        'date_of_birth',
        'ethnicity',
        'hair_color',
        'hair_length',
        'bust_size',
        'height_cm',
        'weight_kg',
        'build',
        'looks',
        'smoker',
        'about',
        'sports',
        'hobbies',
        'zodiac_sign',
        'sexual_orientation',
        'occupation',
        'phone_number',
        'whatsapp_number',
        'show_phone_number',
        'show_whatsapp',
        'is_premium',
        'is_verified',
        'is_featured',
        'featured_expires_at',
        'is_independent',
        'incall_available',
        'outcall_available',
        'is_new',
        'is_online',
        'profile_views',
        'slug',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date_of_birth' => 'date',
        'smoker' => 'boolean',
        'show_phone_number' => 'boolean',
        'show_whatsapp' => 'boolean',
        'is_premium' => 'boolean',
        'is_verified' => 'boolean',
        'is_featured' => 'boolean',
        'featured_expires_at' => 'datetime',
        'is_independent' => 'boolean',
        'incall_available' => 'boolean',
        'outcall_available' => 'boolean',
        'is_new' => 'boolean',
        'is_online' => 'boolean',
        'sports' => 'array',
        'hobbies' => 'array',
    ];

    /**
     * Get the user that owns the escort.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the agency that the escort belongs to.
     */
    public function agency()
    {
        return $this->belongsTo(Agency::class);
    }

    /**
     * Get the images for the escort.
     */
    public function images()
    {
        return $this->hasMany(EscortImage::class);
    }

    /**
     * Get the primary image for the escort.
     */
    public function primaryImage()
    {
        return $this->hasOne(EscortImage::class)->where('is_primary', true);
    }

    /**
     * Get the videos for the escort.
     */
    public function videos()
    {
        return $this->hasMany(EscortVideo::class);
    }

    /**
     * Get the primary video for the escort.
     */
    public function primaryVideo()
    {
        return $this->hasOne(EscortVideo::class)->where('is_primary', true);
    }

    /**
     * Get the services for the escort.
     */
    public function services()
    {
        return $this->belongsToMany(Service::class, 'escort_services');
    }

    /**
     * Get the rates for the escort.
     */
    public function rates()
    {
        return $this->hasMany(EscortRate::class);
    }

    /**
     * Get the locations for the escort.
     */
    public function locations()
    {
        return $this->belongsToMany(Location::class, 'escort_locations')
            ->withPivot('is_primary');
    }

    /**
     * Get the primary location for the escort.
     */
    public function primaryLocation()
    {
        return $this->belongsToMany(Location::class, 'escort_locations')
            ->wherePivot('is_primary', true)
            ->first();
    }

    /**
     * Get the languages for the escort.
     */
    public function languages()
    {
        return $this->belongsToMany(Language::class, 'escort_languages')
            ->withPivot('proficiency');
    }





    /**
     * Get the tours for the escort.
     */
    public function tours()
    {
        return $this->hasMany(Tour::class);
    }



    /**
     * Get the status requests for the escort.
     */
    public function statusRequests()
    {
        return $this->hasMany(EscortStatusRequest::class);
    }

    /**
     * Get the age of the escort.
     */
    public function getAgeAttribute()
    {
        return $this->date_of_birth->age;
    }

    /**
     * Increment the profile views.
     */
    public function incrementViews()
    {
        $this->increment('profile_views');
    }

    /**
     * Scope a query to only include escorts of a specific gender.
     */
    public function scopeGender($query, $gender)
    {
        return $query->where('gender', $gender);
    }

    /**
     * Scope a query to only include verified escorts.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope a query to only include premium escorts.
     */
    public function scopePremium($query)
    {
        return $query->where('is_premium', true);
    }

    /**
     * Scope a query to only include featured escorts.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true)
            ->where(function ($q) {
                $q->whereNull('featured_expires_at')
                  ->orWhere('featured_expires_at', '>', now());
            });
    }

    /**
     * Scope a query to only include independent escorts.
     */
    public function scopeIndependent($query)
    {
        return $query->where('is_independent', true);
    }

    /**
     * Scope a query to only include new escorts.
     */
    public function scopeNew($query)
    {
        return $query->where('is_new', true);
    }

    /**
     * Scope a query to only include online escorts.
     */
    public function scopeOnline($query)
    {
        return $query->where('is_online', true);
    }

    /**
     * Scope a query to only include escorts with incall available.
     */
    public function scopeIncall($query)
    {
        return $query->where('incall_available', true);
    }

    /**
     * Scope a query to only include escorts with outcall available.
     */
    public function scopeOutcall($query)
    {
        return $query->where('outcall_available', true);
    }

    /**
     * Calculate the profile completion percentage.
     */
    public function getProfileCompletionPercentage()
    {
        $fields = [
            'name', 'gender', 'date_of_birth', 'ethnicity', 'hair_color',
            'bust_size', 'height_cm', 'weight_kg', 'about'
        ];

        $completed = 0;
        foreach ($fields as $field) {
            if (!empty($this->$field)) {
                $completed++;
            }
        }

        // Check for images
        if ($this->images->count() > 0) {
            $completed++;
        }

        // Check for services
        if ($this->services->count() > 0) {
            $completed++;
        }

        // Check for rates
        if ($this->rates->count() > 0) {
            $completed++;
        }

        // Check for locations
        if ($this->locations->count() > 0) {
            $completed++;
        }

        return round(($completed / (count($fields) + 4)) * 100);
    }

    /**
     * Check if escort can request verification.
     */
    public function canRequestVerification()
    {
        // Already verified
        if ($this->is_verified) {
            return false;
        }

        // Has pending verification request
        return !$this->statusRequests()
            ->where('request_type', 'verification')
            ->where('status', 'pending')
            ->exists();
    }

    /**
     * Check if escort can request featured status.
     */
    public function canRequestFeatured()
    {
        // Already featured and not expired
        if ($this->is_featured && ($this->featured_expires_at === null || $this->featured_expires_at > now())) {
            return false;
        }

        // Has pending featured request
        return !$this->statusRequests()
            ->where('request_type', 'featured')
            ->where('status', 'pending')
            ->exists();
    }

    /**
     * Get pending verification request.
     */
    public function getPendingVerificationRequest()
    {
        return $this->statusRequests()
            ->where('request_type', 'verification')
            ->where('status', 'pending')
            ->first();
    }

    /**
     * Get pending featured request.
     */
    public function getPendingFeaturedRequest()
    {
        return $this->statusRequests()
            ->where('request_type', 'featured')
            ->where('status', 'pending')
            ->first();
    }

    /**
     * Check if featured status is active (not expired).
     */
    public function isFeaturedActive()
    {
        return $this->is_featured && ($this->featured_expires_at === null || $this->featured_expires_at > now());
    }
}
