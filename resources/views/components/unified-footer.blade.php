@props(['class' => ''])

@php
    // Get footer locations with error handling
    try {
        $footerLocations = \App\Models\Location::active()
            ->ofType('city')
            ->whereHas('escorts', function($query) {
                $query->whereHas('user', function($q) {
                    $q->where('is_active', true);
                });
            })
            ->withCount(['escorts' => function($query) {
                $query->whereHas('user', function($q) {
                    $q->where('is_active', true);
                });
            }])
            ->orderBy('escorts_count', 'desc')
            ->take(6)
            ->get();
    } catch (\Exception $e) {
        $footerLocations = collect([
            (object)['name' => 'Kampala', 'slug' => 'kampala'],
            (object)['name' => 'Entebbe', 'slug' => 'entebbe'],
            (object)['name' => 'Jinja', 'slug' => 'jinja'],
            (object)['name' => '<PERSON><PERSON><PERSON>', 'slug' => 'mbarara'],
            (object)['name' => 'Gulu', 'slug' => 'gulu'],
            (object)['name' => '<PERSON><PERSON>', 'slug' => 'lira'],
        ]);
    }

    // Get footer services
    try {
        $footerServices = \App\Models\Service::whereHas('escorts')
            ->withCount('escorts')
            ->orderBy('escorts_count', 'desc')
            ->take(6)
            ->get();
    } catch (\Exception $e) {
        $footerServices = collect([]);
    }

    // Get settings
    $settings = [];
    try {
        $settings = \App\Models\Setting::pluck('value', 'key')->toArray();
    } catch (\Exception $e) {
        $settings = [
            'site_name' => 'Get Hot Babes',
            'footer_about' => 'The premier platform for high-class escorts and elite companions in Uganda and East Africa.',
            'footer_text' => 'Must be 18+ to use this service.',
        ];
    }
@endphp

<footer class="bg-gray-900 text-white py-12 {{ $class }}">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Company Info -->
            <div class="lg:col-span-1">
                <div class="flex items-center mb-4">
                    <!-- Left Icon - Two People Silhouette -->
                    <svg class="w-6 h-6 mr-3 text-pink-400" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M7 4c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zm10 0c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zM5 9h4c.6 0 1 .4 1 1v9c0 .6-.4 1-1 1H7v-6H6v6H5c-.6 0-1-.4-1-1v-9c0-.6.4-1 1-1zm10 0h4c.6 0 1 .4 1 1v9c0 .6-.4 1-1 1h-1v-6h-1v6h-2c-.6 0-1-.4-1-1v-9c0-.6.4-1 1-1z"/>
                    </svg>

                    <!-- Brand Text -->
                    <h3 class="text-xl font-semibold text-white">
                        <span class="text-pink-500">Get Hot</span> Babes
                    </h3>

                    <!-- Right Icon - Bed -->
                    <svg class="w-6 h-6 ml-3 text-pink-400" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M21 10.78V8c0-1.65-1.35-3-3-3h-4c-.77 0-1.47.3-2 .78-.53-.48-1.23-.78-2-.78H6c-1.65 0-3 1.35-3 3v2.78c-.61.55-1 1.34-1 2.22v6h2v-2h16v2h2v-6c0-.88-.39-1.67-1-2.22zM14 7h4c.55 0 1 .45 1 1v2h-6V8c0-.55.45-1 1-1zM5 8c0-.55.45-1 1-1h4c.55 0 1 .45 1 1v2H5V8zm-1 4h16v2H4v-2z"/>
                    </svg>
                </div>
                <p class="text-gray-400 text-sm mb-4 leading-relaxed">
                    {{ $settings['footer_about'] ?? 'The premier platform for high-class escorts and elite companions in Uganda and East Africa. Connecting discerning clients with sophisticated professionals.' }}
                </p>
                <div class="flex space-x-4">
                    <!-- Social Media Icons (if configured) -->
                    @if(isset($settings['facebook_url']) && $settings['facebook_url'])
                        <a href="{{ $settings['facebook_url'] }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300" target="_blank" rel="noopener">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                        </a>
                    @endif
                    @if(isset($settings['twitter_url']) && $settings['twitter_url'])
                        <a href="{{ $settings['twitter_url'] }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300" target="_blank" rel="noopener">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                            </svg>
                        </a>
                    @endif
                </div>
            </div>

            <!-- Quick Links -->
            <div>
                <h3 class="text-lg font-semibold text-white mb-4">Quick Links</h3>
                <ul class="space-y-2">
                    <li><a href="{{ route('home') }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                        </svg>
                        Browse Escorts
                    </a></li>
                    <li><a href="{{ route('agencies.index') }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-6a1 1 0 00-1-1H9a1 1 0 00-1 1v6a1 1 0 01-1 1H4a1 1 0 110-2V4z"/>
                        </svg>
                        Agencies
                    </a></li>
                    <li><a href="{{ route('locations.index') }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                        </svg>
                        Locations
                    </a></li>
                    @php
                        $adminWhatsApp = \App\Models\Setting::where('key', 'admin_whatsapp')->first()?->value ?? '+256 700 000000';
                        $whatsappNumber = preg_replace('/[^0-9]/', '', $adminWhatsApp);
                    @endphp
                    <li><a href="https://wa.me/{{ $whatsappNumber }}" target="_blank" class="text-gray-400 hover:text-pink-500 transition-colors duration-300 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                            <path d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 222-99.6 222-222 0-59.3-23.1-115-65-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56 81.2 56 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"/>
                        </svg>
                        Contact
                    </a></li>
                    <li><a href="{{ route('register') }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                        </svg>
                        Join Us
                    </a></li>
                </ul>
            </div>

            <!-- Popular Locations -->
            <div>
                <h3 class="text-lg font-semibold text-white mb-4">Popular Locations</h3>
                <ul class="space-y-2">
                    @foreach($footerLocations as $location)
                        <li><a href="{{ route('locations.show', $location->slug) }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                            </svg>
                            {{ $location->name }}
                            @if(isset($location->escorts_count) && $location->escorts_count > 0)
                                <span class="ml-auto text-xs text-gray-500">({{ $location->escorts_count }})</span>
                            @endif
                        </a></li>
                    @endforeach
                </ul>
            </div>

            <!-- Legal & Support -->
            <div>
                <h3 class="text-lg font-semibold text-white mb-4">Legal & Support</h3>
                <ul class="space-y-2">
                    <li><a href="{{ route('terms.index') }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                        </svg>
                        Terms of Service
                    </a></li>
                    <li><a href="{{ route('terms.privacy') }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
                        </svg>
                        Privacy Policy
                    </a></li>
                    <li><a href="{{ route('safety.index') }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        Safety Guidelines
                    </a></li>
                    <li><a href="{{ route('about.index') }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                        </svg>
                        About Us
                    </a></li>
                    <li><a href="{{ route('help.index') }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                        </svg>
                        Help Center
                    </a></li>
                </ul>

                <!-- Call to Action -->
                <div class="mt-6 p-4 bg-gradient-to-r from-pink-600 to-purple-600 rounded-lg">
                    <p class="text-white text-sm font-medium mb-2">Join Our Platform</p>
                    <p class="text-pink-100 text-xs mb-3">Start your journey as an escort or agency</p>
                    <a href="{{ route('register') }}" class="inline-block bg-white text-pink-600 font-medium px-4 py-2 rounded-md text-sm hover:bg-gray-100 transition-colors duration-300">
                        Register Now
                    </a>
                </div>
            </div>
        </div>

        <!-- Bottom Section -->
        <div class="mt-12 pt-8 border-t border-gray-800">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="text-center md:text-left mb-4 md:mb-0">
                    <p class="text-gray-400 text-sm">
                        &copy; {{ date('Y') }} {{ $settings['site_name'] ?? 'Get Hot Babes' }}. All rights reserved.
                    </p>
                    <p class="text-pink-400 text-xs mt-1">
                        {{ $settings['footer_text'] ?? 'Must be 18+ to use this service.' }}
                    </p>
                </div>
                
                <div class="flex flex-wrap justify-center md:justify-end gap-4 text-sm">
                    <a href="{{ route('sitemap.index') }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300">Sitemap</a>
                    <a href="{{ route('robots') }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300">Robots.txt</a>
                    <span class="text-gray-500">|</span>
                    <span class="text-gray-400 text-xs">Made in Uganda 🇺🇬</span>
                </div>
            </div>
        </div>
    </div>
</footer>
