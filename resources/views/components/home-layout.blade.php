<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <!-- SEO Meta Tags -->
        <x-seo-meta :meta="$seoMeta ?? []" :structured-data="$structuredData ?? null" />

        <!-- Favicon -->
        <link rel="icon" href="{{ asset('favicon.svg') }}" type="image/svg+xml">
        <link rel="icon" href="{{ asset('favicon.ico') }}" sizes="any">

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body>
        <div class="min-h-screen bg-gray-50">
            @include('layouts.public-navigation')

            <!-- Page Content -->
            <main>
                {{ $slot }}
            </main>

            <!-- Global Confirmation Modal -->
            <x-confirmation-modal />

            <!-- Footer -->
            <footer class="bg-gray-900 text-white py-10">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                        <div class="col-span-1 md:col-span-2">
                            <h3 class="text-xl font-semibold mb-4">{{ $settings['site_name'] ?? 'Get Hot Babes' }}</h3>
                            <p class="text-gray-400 text-sm mb-4">
                                {{ $settings['footer_about'] ?? 'The premier platform for high-class escorts and elite companions. We connect discerning clients with sophisticated escorts for unforgettable experiences.' }}
                            </p>
                            <p class="text-gray-400 text-sm">
                                &copy; {{ date('Y') }} {{ $settings['site_name'] ?? 'Get Hot Babes' }}. All rights reserved.<br>
                                <span class="text-pink">{{ $settings['footer_text'] ?? 'Must be 18+ to use this service.' }}</span>
                            </p>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                            <ul class="space-y-2 text-sm">
                                <li><a href="{{ route('home') }}" class="text-gray-400 hover:text-pink">Escorts</a></li>
                                <li><a href="{{ route('agencies.index') }}" class="text-gray-400 hover:text-pink">Agencies</a></li>
                                <li><a href="{{ route('locations.index') }}" class="text-gray-400 hover:text-pink">Locations</a></li>
                                @php
                                    $adminWhatsApp = \App\Models\Setting::where('key', 'admin_whatsapp')->first()?->value ?? '+256 700 000000';
                                    $whatsappNumber = preg_replace('/[^0-9]/', '', $adminWhatsApp);
                                @endphp
                                <li><a href="https://wa.me/{{ $whatsappNumber }}" target="_blank" class="text-gray-400 hover:text-pink flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                                        <path d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 222-99.6 222-222 0-59.3-23.1-115-65-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56 81.2 56 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"/>
                                    </svg>
                                    Contact
                                </a></li>
                            </ul>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold mb-4">Legal</h3>
                            <ul class="space-y-2 text-sm">
                                <li><a href="{{ route('terms.index') }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300">Terms of Service</a></li>
                                <li><a href="{{ route('terms.privacy') }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300">Privacy Policy</a></li>
                                <li><a href="{{ route('safety.index') }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300">Safety Guidelines</a></li>
                                <li><a href="{{ route('about.index') }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300">About Us</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </body>
</html>
