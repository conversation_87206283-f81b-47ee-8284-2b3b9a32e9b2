@props([
    'size' => 'default', // default, small, large
    'showText' => true,
    'class' => '',
    'linkRoute' => 'escorts.index'
])

@php
    $sizeClasses = [
        'small' => [
            'container' => 'text-lg',
            'icon' => 'text-lg',
            'spacing' => 'mr-2 ml-2'
        ],
        'default' => [
            'container' => 'text-xl sm:text-2xl',
            'icon' => 'text-xl sm:text-2xl',
            'spacing' => 'mr-2 sm:mr-3 ml-2 sm:ml-3'
        ],
        'large' => [
            'container' => 'text-2xl sm:text-3xl',
            'icon' => 'text-2xl sm:text-3xl',
            'spacing' => 'mr-3 sm:mr-4 ml-3 sm:ml-4'
        ]
    ];
    
    $currentSize = $sizeClasses[$size] ?? $sizeClasses['default'];
@endphp

@php
    $isDashboard = str_contains($class, 'text-gray-800');
    $textColor = $isDashboard ? 'text-gray-800' : 'text-white';
    $hoverTextColor = $isDashboard ? 'group-hover:text-gray-600' : 'group-hover:text-pink-100';
    $iconColor = $isDashboard ? 'text-pink-500' : 'text-pink-400';
    $iconHoverColor = $isDashboard ? 'group-hover:text-pink-600' : 'group-hover:text-pink-300';
@endphp

<a href="{{ route($linkRoute) }}" class="flex items-center {{ $currentSize['container'] }} font-bold {{ $textColor }} whitespace-nowrap group {{ $class }}">
    <!-- Left Icon - Adult Content Icon (Suggestive) -->
    <i class="fas fa-cocktail {{ $currentSize['spacing'] }} {{ $iconColor }} {{ $iconHoverColor }} transition-colors duration-300 {{ $currentSize['icon'] }}"></i>

    @if($showText)
        <!-- Brand Text -->
        <span class="{{ $hoverTextColor }} transition-colors duration-300">
            <span class="text-pink-500">Get Hot</span> Babes
        </span>
    @endif

    <!-- Right Icon - Adult Content Icon (Suggestive) -->
    <i class="fas fa-fire {{ $currentSize['spacing'] }} {{ $iconColor }} {{ $iconHoverColor }} transition-colors duration-300 {{ $currentSize['icon'] }}"></i>
</a>
