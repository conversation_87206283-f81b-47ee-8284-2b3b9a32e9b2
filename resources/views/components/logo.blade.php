@props([
    'size' => 'default', // default, small, large
    'showText' => true,
    'class' => '',
    'linkRoute' => 'escorts.index',
    'theme' => 'auto' // auto, light, dark
])

@php
    $sizeClasses = [
        'small' => [
            'container' => 'text-lg',
            'icon' => 'w-5 h-5',
            'spacing' => 'mr-2 ml-2'
        ],
        'default' => [
            'container' => 'text-xl sm:text-2xl',
            'icon' => 'w-6 h-6 sm:w-7 sm:h-7',
            'spacing' => 'mr-2 sm:mr-3 ml-2 sm:ml-3'
        ],
        'large' => [
            'container' => 'text-2xl sm:text-3xl',
            'icon' => 'w-7 h-7 sm:w-8 sm:h-8',
            'spacing' => 'mr-3 sm:mr-4 ml-3 sm:ml-4'
        ]
    ];

    $currentSize = $sizeClasses[$size] ?? $sizeClasses['default'];

    // Determine theme based on class or explicit theme prop
    if ($theme === 'auto') {
        $isDashboard = str_contains($class, 'text-gray') || str_contains(request()->route()->getName() ?? '', 'dashboard');
        $isGuest = str_contains($class, 'text-gray-400');
    } else {
        $isDashboard = $theme === 'light';
        $isGuest = false;
    }

    // Set colors based on context
    if ($isGuest) {
        $textColor = 'text-gray-400';
        $hoverTextColor = 'group-hover:text-gray-300';
        $iconColor = 'text-gray-400';
        $iconHoverColor = 'group-hover:text-gray-300';
    } elseif ($isDashboard) {
        $textColor = 'text-gray-800';
        $hoverTextColor = 'group-hover:text-gray-600';
        $iconColor = 'text-pink-500';
        $iconHoverColor = 'group-hover:text-pink-600';
    } else {
        $textColor = 'text-white';
        $hoverTextColor = 'group-hover:text-pink-100';
        $iconColor = 'text-pink-400';
        $iconHoverColor = 'group-hover:text-pink-300';
    }
@endphp

<a href="{{ route($linkRoute) }}" class="flex items-center {{ $currentSize['container'] }} font-bold {{ $textColor }} whitespace-nowrap group {{ $class }}">
    <!-- Left Icon - Two People Silhouette -->
    <svg class="{{ $currentSize['icon'] }} mr-2 sm:mr-3 {{ $iconColor }} {{ $iconHoverColor }} transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path d="M7 4c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zm10 0c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zM5 9h4c.6 0 1 .4 1 1v9c0 .6-.4 1-1 1H7v-6H6v6H5c-.6 0-1-.4-1-1v-9c0-.6.4-1 1-1zm10 0h4c.6 0 1 .4 1 1v9c0 .6-.4 1-1 1h-1v-6h-1v6h-2c-.6 0-1-.4-1-1v-9c0-.6.4-1 1-1z"/>
    </svg>

    @if($showText)
        <!-- Brand Text -->
        <span class="{{ $hoverTextColor }} transition-colors duration-300">
            <span class="text-pink-500">Get Hot</span> Babes
        </span>
    @endif

    <!-- Right Icon - Bed -->
    <svg class="{{ $currentSize['icon'] }} ml-2 sm:ml-3 {{ $iconColor }} {{ $iconHoverColor }} transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path d="M21 10.78V8c0-1.65-1.35-3-3-3h-4c-.77 0-1.47.3-2 .78-.53-.48-1.23-.78-2-.78H6c-1.65 0-3 1.35-3 3v2.78c-.61.55-1 1.34-1 2.22v6h2v-2h16v2h2v-6c0-.88-.39-1.67-1-2.22zM14 7h4c.55 0 1 .45 1 1v2h-6V8c0-.55.45-1 1-1zM5 8c0-.55.45-1 1-1h4c.55 0 1 .45 1 1v2H5V8zm-1 4h16v2H4v-2z"/>
    </svg>
</a>
