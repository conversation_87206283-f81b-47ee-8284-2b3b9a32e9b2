@props([
    'size' => 'default', // default, small, large
    'showText' => true,
    'class' => '',
    'linkRoute' => 'escorts.index',
    'theme' => 'auto' // auto, light, dark
])

@php
    $sizeClasses = [
        'small' => [
            'container' => 'text-lg',
            'icon' => 'text-lg',
            'spacing' => 'mr-2 ml-2'
        ],
        'default' => [
            'container' => 'text-xl sm:text-2xl',
            'icon' => 'text-xl sm:text-2xl',
            'spacing' => 'mr-2 sm:mr-3 ml-2 sm:ml-3'
        ],
        'large' => [
            'container' => 'text-2xl sm:text-3xl',
            'icon' => 'text-2xl sm:text-3xl',
            'spacing' => 'mr-3 sm:mr-4 ml-3 sm:ml-4'
        ]
    ];

    $currentSize = $sizeClasses[$size] ?? $sizeClasses['default'];

    // Determine theme based on class or explicit theme prop
    if ($theme === 'auto') {
        $isDashboard = str_contains($class, 'text-gray') || str_contains(request()->route()->getName() ?? '', 'dashboard');
        $isGuest = str_contains($class, 'text-gray-400');
    } else {
        $isDashboard = $theme === 'light';
        $isGuest = false;
    }

    // Set colors based on context
    if ($isGuest) {
        $textColor = 'text-gray-400';
        $hoverTextColor = 'group-hover:text-gray-300';
        $iconColor = 'text-gray-400';
        $iconHoverColor = 'group-hover:text-gray-300';
    } elseif ($isDashboard) {
        $textColor = 'text-gray-800';
        $hoverTextColor = 'group-hover:text-gray-600';
        $iconColor = 'text-pink-500';
        $iconHoverColor = 'group-hover:text-pink-600';
    } else {
        $textColor = 'text-white';
        $hoverTextColor = 'group-hover:text-pink-100';
        $iconColor = 'text-pink-400';
        $iconHoverColor = 'group-hover:text-pink-300';
    }
@endphp

<a href="{{ route($linkRoute) }}" class="flex items-center {{ $currentSize['container'] }} font-bold {{ $textColor }} whitespace-nowrap group {{ $class }}">
    <!-- Left Icon - Adult Content Icon (Cocktail) -->
    <i class="fas fa-cocktail {{ $currentSize['spacing'] }} {{ $iconColor }} {{ $iconHoverColor }} transition-colors duration-300 {{ $currentSize['icon'] }}"></i>

    @if($showText)
        <!-- Brand Text -->
        <span class="{{ $hoverTextColor }} transition-colors duration-300">
            <span class="text-pink-500">Get Hot</span> Babes
        </span>
    @endif

    <!-- Right Icon - Adult Content Icon (Fire) -->
    <i class="fas fa-fire {{ $currentSize['spacing'] }} {{ $iconColor }} {{ $iconHoverColor }} transition-colors duration-300 {{ $currentSize['icon'] }}"></i>
</a>
