<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Agency Dashboard') }}
            </h2>
            <a href="{{ route('profile.agency.edit') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
                {{ __('Edit Agency') }}
            </a>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Welcome Banner -->
            <div style="background: linear-gradient(90deg, #1E40AF, #4F46E5); position: relative;" class="rounded-lg shadow-xl overflow-hidden mb-6 border border-blue-400">
                <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-image: url('data:image/svg+xml,%3Csvg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%23ffffff" fill-opacity="0.1" fill-rule="evenodd"%3E%3Ccircle cx="3" cy="3" r="3"/%3E%3Ccircle cx="13" cy="13" r="3"/%3E%3C/g%3E%3C/svg%3E'); opacity: 0.3;"></div>
                <div class="px-4 py-6 sm:px-6 md:px-8 md:py-8 md:flex md:items-center md:justify-between relative z-10">
                    <div class="w-full">
                        <h2 class="text-xl sm:text-2xl font-bold text-white">Welcome back, {{ Auth::user()->username }}!</h2>
                        @php
                            $agency = Auth::user()->agency;
                            $profileCompletion = $agency ? 70 : 20; // This would be calculated based on agency profile completeness

                            // Calculate status
                            $status = 'Pending Approval';
                            $statusClass = 'bg-yellow-500';

                            if ($agency && $agency->is_approved) {
                                $status = 'Active';
                                $statusClass = 'bg-green-500';

                                if ($agency->is_featured) {
                                    $status = 'Featured';
                                    $statusClass = 'bg-purple-500';
                                }
                            }

                            // Get pending status requests
                            $pendingRequests = $agency ? $agency->statusRequests()->where('status', 'pending')->count() : 0;
                        @endphp
                        <div class="flex flex-wrap items-center mt-2">
                            <p class="text-blue-50 text-sm sm:text-base font-medium mr-2">Your agency profile is {{ $profileCompletion }}% complete</p>
                            <span class="mt-1 sm:mt-0 px-2 py-1 text-xs font-semibold text-white rounded-full {{ $statusClass }}">{{ $status }}</span>
                        </div>
                        <div class="w-full max-w-xs bg-white bg-opacity-20 rounded-full h-2.5 mt-2">
                            <div class="bg-white h-2.5 rounded-full" style="width: {{ $profileCompletion }}%"></div>
                        </div>
                    </div>
                    <div class="mt-4 md:mt-0 flex flex-wrap sm:flex-nowrap gap-2 sm:space-x-2">
                        <a href="{{ route('agency.escorts.create') }}" class="w-full sm:w-auto inline-flex items-center justify-center px-3 sm:px-4 py-2 bg-white text-blue-700 border border-white rounded-md font-semibold text-xs uppercase tracking-widest hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-700 transition ease-in-out duration-150 shadow-md">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                            {{ __('Add New Escort') }}
                        </a>
                        @if($pendingRequests > 0)
                        <a href="{{ route('agency.status-requests.index') }}" class="w-full sm:w-auto inline-flex items-center justify-center px-3 sm:px-4 py-2 bg-yellow-500 bg-opacity-90 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-opacity-100 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-yellow-500 transition ease-in-out duration-150">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {{ $pendingRequests }} Pending Request{{ $pendingRequests > 1 ? 's' : '' }}
                        </a>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6">
                <div class="bg-white rounded-lg shadow-md overflow-hidden border-t-4 border-blue-500 transition-all duration-300 hover:shadow-lg">
                    <div class="p-4 sm:p-5 bg-gradient-to-br from-white to-blue-50">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 bg-blue-100 rounded-md p-2 sm:p-3 shadow-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                            </div>
                            <div class="ml-3 sm:ml-4">
                                <h3 class="text-base sm:text-lg font-medium text-gray-900">Escorts</h3>
                                <p class="text-2xl sm:text-3xl font-bold text-gray-700">{{ $metrics['escorts_count'] }}</p>
                                @php
                                    $verifiedPercent = $metrics['escorts_count'] > 0
                                        ? round(($metrics['verified_escorts'] / $metrics['escorts_count']) * 100)
                                        : 0;
                                @endphp
                                <span class="text-xs text-gray-500">{{ $metrics['verified_escorts'] }} verified ({{ $verifiedPercent }}%)</span>
                            </div>
                        </div>
                        <a href="{{ route('agency.escorts') }}" class="mt-3 sm:mt-4 inline-flex items-center text-xs sm:text-sm font-medium text-blue-600 hover:text-blue-800">
                            Manage escorts
                            <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </a>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md overflow-hidden border-t-4 border-indigo-500 transition-all duration-300 hover:shadow-lg">
                    <div class="p-4 sm:p-5 bg-gradient-to-br from-white to-indigo-50">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 bg-indigo-100 rounded-md p-2 sm:p-3 shadow-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </div>
                            <div class="ml-3 sm:ml-4">
                                <h3 class="text-base sm:text-lg font-medium text-gray-900">Locations</h3>
                                <p class="text-2xl sm:text-3xl font-bold text-gray-700">{{ ($agency && $agency->locations) ? $agency->locations->count() : 0 }}</p>
                                <span class="text-xs text-gray-500">Service areas</span>
                            </div>
                        </div>
                        <a href="{{ route('agency.locations') }}" class="mt-3 sm:mt-4 inline-flex items-center text-xs sm:text-sm font-medium text-indigo-600 hover:text-indigo-800">
                            Manage locations
                            <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </a>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md overflow-hidden border-t-4 border-purple-500 transition-all duration-300 hover:shadow-lg">
                    <div class="p-4 sm:p-5 bg-gradient-to-br from-white to-purple-50">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 bg-purple-100 rounded-md p-2 sm:p-3 shadow-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                            </div>
                            <div class="ml-3 sm:ml-4">
                                <h3 class="text-base sm:text-lg font-medium text-gray-900">Profile Views</h3>
                                <p class="text-2xl sm:text-3xl font-bold text-gray-700">{{ $agency->profile_views ?? 0 }}</p>
                                @php
                                    // Calculate daily average based on total views
                                    $dailyAvg = ($agency->profile_views ?? 0) > 0 ? round($agency->profile_views / 30) : 0;
                                @endphp
                                <span class="text-xs text-gray-500">~{{ $dailyAvg }} views/day</span>
                            </div>
                        </div>
                        <a href="{{ route('profile.agency.edit') }}" class="mt-3 sm:mt-4 inline-flex items-center text-xs sm:text-sm font-medium text-purple-600 hover:text-purple-800">
                            View profile
                            <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </a>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md overflow-hidden border-t-4 border-green-500 transition-all duration-300 hover:shadow-lg">
                    <div class="p-4 sm:p-5 bg-gradient-to-br from-white to-green-50">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 bg-green-100 rounded-md p-2 sm:p-3 shadow-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="ml-3 sm:ml-4">
                                <h3 class="text-base sm:text-lg font-medium text-gray-900">Status</h3>
                                @php
                                    $statusRequests = ($agency) ? $agency->statusRequests()->latest()->take(1)->first() : null;
                                    $requestStatus = $statusRequests ? ucfirst($statusRequests->status) : 'None';
                                    $requestType = $statusRequests ? ucfirst($statusRequests->request_type) : '';
                                @endphp
                                <p class="text-lg sm:text-xl font-bold text-gray-700">{{ $requestStatus }}</p>
                                <span class="text-xs text-gray-500">{{ $requestType }}</span>
                            </div>
                        </div>
                        <a href="{{ route('agency.status-requests.index') }}" class="mt-3 sm:mt-4 inline-flex items-center text-xs sm:text-sm font-medium text-green-600 hover:text-green-800">
                            View requests
                            <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
                <!-- Left Column -->
                <div class="lg:col-span-2 space-y-4 sm:space-y-6">
                    <!-- Financial Overview -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                        <div class="border-b border-gray-200">
                            <div class="p-5 flex justify-between items-center">
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900">Financial Overview</h3>
                                    <p class="text-sm text-gray-500">Summary of your agency's financial status</p>
                                </div>
                                <div>
                                    <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">Active</span>
                                </div>
                            </div>
                        </div>
                        <div class="p-5">
                            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <h4 class="text-xs font-medium text-gray-500 uppercase">Current Plan</h4>
                                    <p class="text-xl font-bold text-gray-900 mt-1">
                                        @if($agency && $agency->is_featured)
                                            Featured
                                        @elseif($agency && $agency->is_approved)
                                            Standard
                                        @else
                                            Pending
                                        @endif
                                    </p>
                                    @php
                                        $expiryDate = ($agency) ? $agency->statusRequests()->where('status', 'approved')->latest()->first()?->expires_at : null;
                                    @endphp
                                    @if($expiryDate)
                                        <p class="text-xs text-gray-500 mt-1">Expires: {{ $expiryDate->format('M d, Y') }}</p>
                                    @endif
                                </div>
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <h4 class="text-xs font-medium text-gray-500 uppercase">Last Payment</h4>
                                    <p class="text-xl font-bold text-gray-900 mt-1">
                                        @php
                                            $lastPayment = ($agency) ? $agency->statusRequests()->where('status', 'approved')->latest()->first()?->price : 0;
                                        @endphp
                                        UGX {{ number_format($lastPayment ?? 0, 0) }}
                                    </p>
                                    <p class="text-xs text-gray-500 mt-1">
                                        @php
                                            $lastPaymentDate = ($agency) ? $agency->statusRequests()->where('status', 'approved')->latest()->first()?->approved_at : null;
                                        @endphp
                                        {{ $lastPaymentDate ? $lastPaymentDate->format('M d, Y') : 'No payments yet' }}
                                    </p>
                                </div>
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <h4 class="text-xs font-medium text-gray-500 uppercase">Next Payment</h4>
                                    <p class="text-xl font-bold text-gray-900 mt-1">
                                        @if($expiryDate && $expiryDate->gt(now()))
                                            {{ $expiryDate->diffInDays(now()) }} days
                                        @else
                                            Due Now
                                        @endif
                                    </p>
                                    <a href="{{ route('agency.status-requests.create') }}" class="text-xs text-blue-600 hover:text-blue-800 mt-1 inline-block">Renew subscription</a>
                                </div>
                            </div>

                            <div class="mt-4 flex justify-between items-center">
                                <h4 class="text-sm font-medium text-gray-700">Payment History</h4>
                                <a href="{{ route('agency.status-requests.index') }}" class="text-xs text-blue-600 hover:text-blue-800">View all</a>
                            </div>
                            <div class="mt-2 bg-gray-50 rounded-lg p-4">
                                @php
                                    $paymentHistory = ($agency) ? $agency->statusRequests()->where('status', 'approved')->latest()->take(3)->get() : collect([]);
                                @endphp
                                @if($paymentHistory->isEmpty())
                                    <div class="text-center py-4">
                                        <p class="text-sm text-gray-500">No payment history available</p>
                                    </div>
                                @else
                                    <div class="space-y-3">
                                        @foreach($paymentHistory as $payment)
                                            <div class="flex justify-between items-center border-b border-gray-200 pb-2 last:border-0 last:pb-0">
                                                <div>
                                                    <p class="text-sm font-medium text-gray-900">{{ ucfirst($payment->request_type) }} ({{ ucfirst($payment->duration) }})</p>
                                                    <p class="text-xs text-gray-500">{{ $payment->approved_at->format('M d, Y') }}</p>
                                                </div>
                                                <div>
                                                    <p class="text-sm font-medium text-gray-900">UGX {{ number_format($payment->price, 0) }}</p>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Analytics Overview -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                        <div class="border-b border-gray-200">
                            <div class="p-5">
                                <h3 class="text-lg font-medium text-gray-900">Performance Analytics</h3>
                                <p class="text-sm text-gray-500">Overview of your agency's performance</p>
                            </div>
                        </div>
                        <div class="p-5">
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">Profile Engagement</h4>
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-2xl font-bold text-gray-900">{{ $agency->profile_views ?? 0 }}</p>
                                            <p class="text-xs text-gray-500">Total views</p>
                                        </div>
                                        <div class="h-16 w-16">
                                            <div class="w-full h-full rounded-full bg-blue-100 flex items-center justify-center">
                                                @php
                                                    $growth = $metrics['escort_growth'] ?? 0;
                                                    $growthSign = $growth >= 0 ? '+' : '';
                                                    $growthClass = $growth >= 0 ? 'text-blue-600' : 'text-red-600';
                                                @endphp
                                                <span class="{{ $growthClass }} font-bold">{{ $growthSign }}{{ $growth }}%</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-2 h-2 bg-gray-200 rounded-full overflow-hidden">
                                        <div class="h-full bg-blue-500 rounded-full" style="width: {{ $metrics['performance_score'] ?? 0 }}%"></div>
                                    </div>
                                </div>
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">Escort Visibility</h4>
                                    <div class="flex items-center justify-between">
                                        <div>
                                            @php
                                                $totalEscorts = $metrics['escorts_count'];
                                                $featuredEscorts = $metrics['featured_escorts'];
                                                $featuredPercent = $totalEscorts > 0 ? round(($featuredEscorts / $totalEscorts) * 100) : 0;
                                            @endphp
                                            <p class="text-2xl font-bold text-gray-900">{{ $featuredEscorts }}/{{ $totalEscorts }}</p>
                                            <p class="text-xs text-gray-500">Featured escorts</p>
                                        </div>
                                        <div class="h-16 w-16">
                                            <div class="w-full h-full rounded-full bg-purple-100 flex items-center justify-center">
                                                <span class="text-purple-600 font-bold">{{ $featuredPercent }}%</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-2 h-2 bg-gray-200 rounded-full overflow-hidden">
                                        <div class="h-full bg-purple-500 rounded-full" style="width: {{ $featuredPercent }}%"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <h4 class="text-sm font-medium text-gray-700 mb-2">Monthly Performance</h4>
                                <div class="bg-gray-50 rounded-lg p-4 h-48 flex items-center justify-center">
                                    <div class="text-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                        </svg>
                                        <p class="mt-2 text-sm text-gray-500">Analytics data will appear here</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Escorts List -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                        <div class="border-b border-gray-200">
                            <div class="p-5">
                                <h3 class="text-lg font-medium text-gray-900">Your Escorts</h3>
                                <p class="text-sm text-gray-500">Manage your agency's escorts</p>
                            </div>
                        </div>
                        <div class="p-5">
                            @if($agency && $agency->escorts && $agency->escorts->count() > 0)
                                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    @foreach($agency->escorts->take(4) as $escort)
                                        <div class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer">
                                            <div class="flex-shrink-0 h-12 w-12 rounded-full bg-gray-200 overflow-hidden">
                                                @if($escort->images && $escort->images->count() > 0)
                                                    <img src="{{ asset('storage/' . $escort->images->first()->path) }}" alt="{{ $escort->name }}" class="h-full w-full object-cover">
                                                @endif
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900">{{ $escort->name }}</p>
                                                <p class="text-xs text-gray-500">{{ ucfirst($escort->gender) }}, {{ $escort->age }} years</p>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                                <div class="mt-4 text-center">
                                    <a href="{{ route('agency.escorts') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                        Manage All Escorts
                                    </a>
                                </div>
                            @else
                                <div class="text-center py-8">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                                    </svg>
                                    <p class="mt-2 text-gray-500">You don't have any escorts yet.</p>
                                    <div class="mt-4">
                                        <a href="{{ route('agency.escorts.create') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                            Add Your First Escort
                                        </a>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                        <div class="border-b border-gray-200">
                            <div class="p-5">
                                <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
                            </div>
                        </div>
                        <div class="p-5">
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <a href="{{ route('profile.agency.edit') }}" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                                    <div class="flex-shrink-0 bg-blue-100 rounded-md p-3">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="text-sm font-medium text-gray-900">Edit Agency Profile</h4>
                                        <p class="text-xs text-gray-500">Update your agency information</p>
                                    </div>
                                </a>
                                <a href="{{ route('agency.escorts.create') }}" class="flex items-center p-4 bg-indigo-50 rounded-lg hover:bg-indigo-100 transition-colors">
                                    <div class="flex-shrink-0 bg-indigo-100 rounded-md p-3">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="text-sm font-medium text-gray-900">Add New Escort</h4>
                                        <p class="text-xs text-gray-500">Register a new escort to your agency</p>
                                    </div>
                                </a>
                                <a href="{{ route('agency.locations') }}" class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                                    <div class="flex-shrink-0 bg-purple-100 rounded-md p-3">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="text-sm font-medium text-gray-900">Manage Locations</h4>
                                        <p class="text-xs text-gray-500">Set your agency's service areas</p>
                                    </div>
                                </a>

                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-6">
                    <!-- Notifications -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                        <div class="border-b border-gray-200">
                            <div class="p-5 flex justify-between items-center">
                                <h3 class="text-lg font-medium text-gray-900">Notifications</h3>
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">3 New</span>
                            </div>
                        </div>
                        <div class="p-5">
                            <div class="space-y-4">
                                <div class="bg-blue-50 p-3 rounded-lg">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900">Welcome to your agency dashboard!</p>
                                            <p class="text-xs text-gray-500 mt-1">Complete your profile to attract more clients.</p>
                                            <p class="text-xs text-gray-400 mt-1">Just now</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-gray-50 p-3 rounded-lg">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900">Add your first escort</p>
                                            <p class="text-xs text-gray-500 mt-1">Start building your agency roster.</p>
                                            <p class="text-xs text-gray-400 mt-1">1 hour ago</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-gray-50 p-3 rounded-lg">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900">Set your service locations</p>
                                            <p class="text-xs text-gray-500 mt-1">Help clients find your escorts in their area.</p>
                                            <p class="text-xs text-gray-400 mt-1">2 hours ago</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4 text-center">
                                <a href="{{ route('notifications.index') }}" class="text-sm text-blue-600 hover:text-blue-800">View all notifications</a>
                            </div>
                        </div>
                    </div>

                    <!-- Marketing Tips -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                        <div class="border-b border-gray-200">
                            <div class="p-5 flex justify-between items-center">
                                <h3 class="text-lg font-medium text-gray-900">Marketing Tips</h3>
                                <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">New</span>
                            </div>
                        </div>
                        <div class="p-5">
                            <div class="space-y-4">
                                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-100">
                                    <h4 class="text-sm font-semibold text-blue-800 mb-2">Boost Your Agency Visibility</h4>
                                    <p class="text-xs text-gray-700 mb-3">Increase your agency's visibility and attract more clients with these proven strategies:</p>

                                    <ul class="space-y-2">
                                        <li class="flex items-start">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-600 mt-0.5 mr-1.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <span class="text-xs text-gray-700">Request <span class="font-medium">featured status</span> to appear at the top of search results</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-600 mt-0.5 mr-1.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <span class="text-xs text-gray-700">Add <span class="font-medium">high-quality photos</span> of your escorts to increase engagement</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-600 mt-0.5 mr-1.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <span class="text-xs text-gray-700">Expand your <span class="font-medium">service locations</span> to reach more potential clients</span>
                                        </li>
                                    </ul>

                                    <div class="mt-3 text-right">
                                        <a href="{{ route('agency.status-requests.create') }}" class="inline-flex items-center text-xs font-medium text-blue-700 hover:text-blue-900">
                                            Get featured now
                                            <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </a>
                                    </div>
                                </div>

                                <div class="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-100">
                                    <h4 class="text-sm font-semibold text-purple-800 mb-2">Optimize Your Escort Profiles</h4>
                                    <p class="text-xs text-gray-700 mb-3">Help your escorts stand out from the competition:</p>

                                    <ul class="space-y-2">
                                        <li class="flex items-start">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-purple-600 mt-0.5 mr-1.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <span class="text-xs text-gray-700">Write <span class="font-medium">detailed descriptions</span> that highlight unique qualities</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-purple-600 mt-0.5 mr-1.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <span class="text-xs text-gray-700">Request <span class="font-medium">verification</span> for your top escorts to build trust</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-purple-600 mt-0.5 mr-1.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <span class="text-xs text-gray-700">Keep <span class="font-medium">rates competitive</span> and clearly displayed</span>
                                        </li>
                                    </ul>

                                    <div class="mt-3 text-right">
                                        <a href="{{ route('agency.escorts') }}" class="inline-flex items-center text-xs font-medium text-purple-700 hover:text-purple-900">
                                            Manage escorts
                                            <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Agency Status -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                        <div class="border-b border-gray-200">
                            <div class="p-5">
                                <h3 class="text-lg font-medium text-gray-900">Agency Status</h3>
                            </div>
                        </div>
                        <div class="p-5">
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-700">Approval Status</span>
                                    @if($agency && $agency->is_approved)
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Approved</span>
                                    @else
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending Approval</span>
                                    @endif
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-700">Verification Status</span>
                                    @if($agency && $agency->is_verified)
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Verified</span>
                                    @else
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                                    @endif
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-700">Featured Status</span>
                                    @if($agency && $agency->is_featured)
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">Featured</span>
                                    @else
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Not Featured</span>
                                    @endif
                                </div>

                                @php
                                    $expiryDate = ($agency) ? $agency->statusRequests()->where('status', 'approved')->latest()->first()?->expires_at : null;
                                @endphp
                                @if($expiryDate)
                                <div class="flex items-center justify-between mt-2">
                                    <span class="text-sm font-medium text-gray-700">Expires On</span>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $expiryDate->lt(now()) ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800' }}">
                                        {{ $expiryDate->format('M d, Y') }}
                                    </span>
                                </div>
                                @endif

                                @if(!$agency->is_approved)
                                <div class="mt-6 text-center">
                                    <a href="{{ route('agency.status-requests.create') }}" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        Request Approval
                                    </a>
                                </div>
                                @elseif(!$agency->is_featured)
                                <div class="mt-6 text-center">
                                    <a href="{{ route('agency.status-requests.create') }}" class="inline-flex items-center px-4 py-2 bg-purple-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-purple-700 focus:bg-purple-700 active:bg-purple-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                                        </svg>
                                        Request Featured Status
                                    </a>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Account Settings -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                        <div class="border-b border-gray-200">
                            <div class="p-5">
                                <h3 class="text-lg font-medium text-gray-900">Account Settings</h3>
                            </div>
                        </div>
                        <div class="p-5">
                            <nav class="space-y-2">
                                <a href="{{ route('profile.edit') }}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-50 hover:text-blue-600" aria-label="Edit Account">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    Edit Account
                                </a>
                                <a href="{{ route('password.change') }}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-50 hover:text-blue-600" aria-label="Change Password">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                    </svg>
                                    Change Password
                                </a>
                                <a href="{{ route('notifications.index') }}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-50 hover:text-blue-600" aria-label="Notifications">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                                    </svg>
                                    Notifications
                                </a>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
