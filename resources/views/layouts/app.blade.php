<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" prefix="og: https://ogp.me/ns#">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        @php
            $seoService = app(\App\Services\SeoService::class);
            $meta = $seoService->generateMetaTags($seoMeta ?? []);
            $ogTags = $seoService->generateOpenGraphTags($meta);
            $twitterTags = $seoService->generateTwitterCardTags($meta);
            $robotsTag = $seoService->generateRobotsTag($seoContentType ?? 'public');
        @endphp

        <!-- SEO Meta Tags -->
        <title>{{ $meta['title'] }}</title>
        <meta name="description" content="{{ $meta['description'] }}">
        <meta name="keywords" content="{{ $meta['keywords'] }}">
        <meta name="robots" content="{{ $robotsTag }}">
        <meta name="author" content="Get Hot Babes">
        <link rel="canonical" href="{{ $meta['canonical'] }}">

        <!-- Open Graph Meta Tags -->
        @foreach($ogTags as $property => $content)
            @if($content)
                <meta property="{{ $property }}" content="{{ $content }}">
            @endif
        @endforeach

        <!-- Twitter Card Meta Tags -->
        @foreach($twitterTags as $name => $content)
            @if($content)
                <meta name="{{ $name }}" content="{{ $content }}">
            @endif
        @endforeach

        <!-- Geo Meta Tags for Uganda/East Africa -->
        <meta name="geo.region" content="UG">
        <meta name="geo.placename" content="Uganda">
        <meta name="geo.position" content="1.3733;32.2903">
        <meta name="ICBM" content="1.3733, 32.2903">

        <!-- Search Engine Verification -->
        @php
            $googleVerification = \App\Models\Setting::getValue('google_search_console_verification');
            $bingVerification = \App\Models\Setting::getValue('bing_webmaster_verification');
        @endphp
        @if($googleVerification)
            <meta name="google-site-verification" content="{{ $googleVerification }}">
        @endif
        @if($bingVerification)
            <meta name="msvalidate.01" content="{{ $bingVerification }}">
        @endif

        <!-- Hreflang Tags for East Africa -->
        @php
            $hreflangTags = $seoService->generateHreflangTags();
        @endphp
        @foreach($hreflangTags as $hreflang => $url)
            <link rel="alternate" hreflang="{{ $hreflang }}" href="{{ $url }}">
        @endforeach

        <!-- Favicon -->
        <link rel="icon" href="{{ asset('favicon.svg') }}" type="image/svg+xml">
        <link rel="icon" href="{{ asset('favicon.ico') }}" sizes="any">
        <link rel="apple-touch-icon" href="{{ asset('apple-touch-icon.png') }}">

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">

        <!-- Font Awesome Icons -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

        <!-- Flaticons CDN -->
        <link rel='stylesheet' href='https://cdn-uicons.flaticon.com/2.6.0/uicons-bold-rounded/css/uicons-bold-rounded.css'>
        <link rel='stylesheet' href='https://cdn-uicons.flaticon.com/2.6.0/uicons-regular-rounded/css/uicons-regular-rounded.css'>

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <!-- Structured Data -->
        @if(isset($structuredData))
            <script type="application/ld+json">
                {!! json_encode($structuredData, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) !!}
            </script>
        @endif

        <!-- Analytics Tracking (only if enabled) -->
        @php
            $analyticsEnabled = \App\Models\Setting::getValue('analytics_enabled', false);
            $googleAnalyticsId = \App\Models\Setting::getValue('google_analytics_id');
            $facebookPixelId = \App\Models\Setting::getValue('facebook_pixel_id');
            $hotjarId = \App\Models\Setting::getValue('hotjar_id');
        @endphp

        @if($analyticsEnabled)
            <!-- Google Analytics -->
            @if($googleAnalyticsId)
                <!-- Google tag (gtag.js) -->
                <script async src="https://www.googletagmanager.com/gtag/js?id={{ $googleAnalyticsId }}"></script>
                <script>
                    window.dataLayer = window.dataLayer || [];
                    function gtag(){dataLayer.push(arguments);}
                    gtag('js', new Date());
                    gtag('config', '{{ $googleAnalyticsId }}');
                </script>
            @endif

            <!-- Facebook Pixel -->
            @if($facebookPixelId)
                <script>
                    !function(f,b,e,v,n,t,s)
                    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
                    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
                    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
                    n.queue=[];t=b.createElement(e);t.async=!0;
                    t.src=v;s=b.getElementsByTagName(e)[0];
                    s.parentNode.insertBefore(t,s)}(window, document,'script',
                    'https://connect.facebook.net/en_US/fbevents.js');
                    fbq('init', '{{ $facebookPixelId }}');
                    fbq('track', 'PageView');
                </script>
                <noscript><img height="1" width="1" style="display:none"
                    src="https://www.facebook.com/tr?id={{ $facebookPixelId }}&ev=PageView&noscript=1"
                /></noscript>
            @endif

            <!-- Hotjar Tracking -->
            @if($hotjarId)
                <script>
                    (function(h,o,t,j,a,r){
                        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                        h._hjSettings={hjid:{{ $hotjarId }},hjsv:6};
                        a=o.getElementsByTagName('head')[0];
                        r=o.createElement('script');r.async=1;
                        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                        a.appendChild(r);
                    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
                </script>
            @endif
        @endif

        <!-- Preload critical resources -->
        <link rel="preload" href="{{ asset('images/hero-bg.jpg') }}" as="image" type="image/jpeg">
        <link rel="dns-prefetch" href="//fonts.googleapis.com">
        <link rel="dns-prefetch" href="//fonts.bunny.net">
        <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">

    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen bg-gray-50">
            @include('layouts.navigation')

            <!-- Page Heading -->
            @if (isset($header))
                <header class="bg-white shadow">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        {{ $header }}
                    </div>
                </header>
            @endif

            <!-- Admin Notifications -->
            @auth
                @if(auth()->user()->notifications()->unread()->where('type', 'admin_action')->orWhere('type', 'announcement')->count() > 0)
                    <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
                        <div class="space-y-2">
                            @foreach(auth()->user()->notifications()->unread()->where(function($query) {
                                $query->where('type', 'admin_action')->orWhere('type', 'announcement');
                            })->latest()->take(3)->get() as $notification)
                                <x-notification-alert :notification="$notification" />
                            @endforeach

                            @if(auth()->user()->notifications()->unread()->where(function($query) {
                                $query->where('type', 'admin_action')->orWhere('type', 'announcement');
                            })->count() > 3)
                                <div class="text-center py-2">
                                    <a href="{{ route('notifications.index') }}" class="text-sm text-indigo-600 hover:text-indigo-900">View all notifications</a>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif
            @endauth

            <!-- Page Content -->
            <main>
                {{ $slot }}
            </main>

            <!-- Global Confirmation Modal -->
            <x-confirmation-modal />
        </div>

        <!-- Age Disclaimer Modal -->
        <x-age-disclaimer />
    </body>
</html>
