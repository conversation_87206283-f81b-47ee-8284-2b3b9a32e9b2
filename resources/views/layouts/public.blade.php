<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <!-- SEO Meta Tags -->
        <x-seo-meta :meta="$seoMeta ?? []" :structured-data="$structuredData ?? null" />

        <!-- Performance Optimization -->
        <x-performance-optimization />

        <!-- PWA Manifest -->
        <link rel="manifest" href="{{ route('manifest') }}">
        <meta name="theme-color" content="#ec4899">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="default">
        <meta name="apple-mobile-web-app-title" content="Get Hot Babes">

        <!-- Favicon -->
        <link rel="icon" href="{{ asset('favicon.svg') }}" type="image/svg+xml">
        <link rel="icon" href="{{ asset('favicon.ico') }}" sizes="any">
        <link rel="apple-touch-icon" href="{{ asset('images/icons/icon-192x192.png') }}">

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <!-- Analytics and SEO Tracking -->
        <x-analytics-seo page="general" />
    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen bg-gray-50">
            @include('layouts.public-navigation')

            <!-- Page Content -->
            <main>
                {{ $slot }}
            </main>

            <!-- Global Confirmation Modal -->
            <x-confirmation-modal />

            <!-- Footer -->
            <footer class="bg-gray-900 text-gray-300">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                        <div class="col-span-1 sm:col-span-2 lg:col-span-1">
                            <div class="flex items-center mb-4">
                                <!-- Left Icon - Two People Silhouette -->
                                <svg class="w-6 h-6 mr-3 text-pink-400" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M7 4c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zm10 0c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zM5 9h4c.6 0 1 .4 1 1v9c0 .6-.4 1-1 1H7v-6H6v6H5c-.6 0-1-.4-1-1v-9c0-.6.4-1 1-1zm10 0h4c.6 0 1 .4 1 1v9c0 .6-.4 1-1 1h-1v-6h-1v6h-2c-.6 0-1-.4-1-1v-9c0-.6.4-1 1-1z"/>
                                </svg>

                                <h3 class="text-xl font-bold text-white">{{ $settings['site_name'] ?? 'Get Hot Babes' }}</h3>

                                <!-- Right Icon - Bed -->
                                <svg class="w-6 h-6 ml-3 text-pink-400" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M21 10.78V8c0-1.65-1.35-3-3-3h-4c-.77 0-1.47.3-2 .78-.53-.48-1.23-.78-2-.78H6c-1.65 0-3 1.35-3 3v2.78c-.61.55-1 1.34-1 2.22v6h2v-2h16v2h2v-6c0-.88-.39-1.67-1-2.22zM14 7h4c.55 0 1 .45 1 1v2h-6V8c0-.55.45-1 1-1zM5 8c0-.55.45-1 1-1h4c.55 0 1 .45 1 1v2H5V8zm-1 4h16v2H4v-2z"/>
                                </svg>
                            </div>
                            <p class="text-gray-400 mb-4">{{ $settings['footer_about'] ?? 'The premier escort directory for high-class companions.' }}</p>
                        </div>

                        <div>
                            <h3 class="text-lg font-semibold text-white mb-4">Quick Links</h3>
                            <ul class="space-y-2">
                                <li><a href="{{ route('escorts.index') }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                    </svg>
                                    Escorts
                                </a></li>
                                <li><a href="{{ route('agencies.index') }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                    </svg>
                                    Agencies
                                </a></li>
                                <li><a href="{{ route('locations.index') }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                    </svg>
                                    Locations
                                </a></li>
                                @php
                                    $adminWhatsApp = \App\Models\Setting::where('key', 'admin_whatsapp')->first()?->value ?? '+256 700 000000';
                                    $whatsappNumber = preg_replace('/[^0-9]/', '', $adminWhatsApp);
                                @endphp
                                <li><a href="https://wa.me/{{ $whatsappNumber }}" target="_blank" class="text-gray-400 hover:text-pink-500 transition-colors duration-300 flex items-center">
                                    <svg class="h-4 w-4 mr-2" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                                        <path d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 222-99.6 222-222 0-59.3-23.1-115-65-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56 81.2 56 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"/>
                                    </svg>
                                    Contact
                                </a></li>
                            </ul>
                        </div>

                        <div>
                            <h3 class="text-lg font-semibold text-white mb-4">Popular Locations</h3>
                            <ul class="space-y-2">
                                @php
                                    try {
                                        $footerLocations = \App\Models\Location::active()
                                            ->ofType('city')
                                            ->whereHas('escorts')
                                            ->withCount('escorts')
                                            ->orderBy('escorts_count', 'desc')
                                            ->take(5)
                                            ->get();
                                    } catch (\Exception $e) {
                                        $footerLocations = collect([
                                            (object)['name' => 'Kampala', 'slug' => 'kampala'],
                                            (object)['name' => 'Entebbe', 'slug' => 'entebbe'],
                                            (object)['name' => 'Jinja', 'slug' => 'jinja'],
                                            (object)['name' => 'Mbarara', 'slug' => 'mbarara'],
                                            (object)['name' => 'Gulu', 'slug' => 'gulu'],
                                        ]);
                                    }
                                @endphp
                                @foreach($footerLocations as $location)
                                    <li><a href="{{ route('locations.show', $location->slug) }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300 flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                        </svg>
                                        <span class="truncate">{{ $location->name }}</span>
                                    </a></li>
                                @endforeach
                            </ul>
                        </div>

                        <div>
                            <h3 class="text-lg font-semibold text-white mb-4">Contact Us</h3>
                            <p class="text-gray-400 mb-2">Have questions or need assistance?</p>
                            @php
                                $adminWhatsApp = \App\Models\Setting::where('key', 'admin_whatsapp')->first()?->value ?? '+256 700 000000';
                                $whatsappNumber = preg_replace('/[^0-9]/', '', $adminWhatsApp);
                            @endphp
                            <a href="https://wa.me/{{ $whatsappNumber }}" target="_blank" class="text-pink-500 hover:text-white transition-colors duration-300 flex items-center w-fit">
                                <svg class="h-5 w-5 mr-2" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                                    <path d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 222-99.6 222-222 0-59.3-23.1-115-65-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56 81.2 56 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"/>
                                </svg>
                                Contact Us
                            </a>
                            <p class="text-gray-400 mt-4 mb-2">Join as an escort or agency:</p>
                            <a href="{{ route('register') }}" class="inline-flex items-center bg-pink-500 hover:bg-pink-600 text-white font-medium px-4 py-2 rounded-md transition-colors duration-300">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                                </svg>
                                Register Now
                            </a>
                        </div>
                    </div>

                    <div class="mt-8 sm:mt-12 pt-6 sm:pt-8 border-t border-gray-800 text-center text-gray-400 text-sm">
                        <p>&copy; {{ date('Y') }} {{ $settings['site_name'] ?? 'Get Hot Babes' }}. All rights reserved.</p>
                        <div class="mt-2 flex flex-wrap justify-center gap-4">
                            <a href="{{ route('terms.index') }}" class="hover:text-pink-500 transition-colors duration-300">Terms of Service</a>
                            <a href="{{ route('terms.privacy') }}" class="hover:text-pink-500 transition-colors duration-300">Privacy Policy</a>
                            @php
                                $adminWhatsApp = \App\Models\Setting::where('key', 'admin_whatsapp')->first()?->value ?? '+256 700 000000';
                                $whatsappNumber = preg_replace('/[^0-9]/', '', $adminWhatsApp);
                            @endphp
                            <a href="https://wa.me/{{ $whatsappNumber }}" target="_blank" class="hover:text-pink-500 transition-colors duration-300">Contact Us</a>
                        </div>
                        <p class="mt-4 max-w-2xl mx-auto">
                            {{ $settings['footer_text'] ?? 'This website contains adult content and is only suitable for those who are 18 years or older.' }}
                        </p>
                    </div>
                </div>
            </footer>
        </div>

        <!-- Age Disclaimer Modal -->
        <x-age-disclaimer />

        <!-- Analytics Tracking -->
        <x-analytics-tracking
            :page="$analyticsPage ?? 'general'"
            :entity="$analyticsEntity ?? null"
        />
    </body>
</html>
