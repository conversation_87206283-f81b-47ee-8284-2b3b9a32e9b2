<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class EscortsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First create some users for the escorts
        $users = [
            [
                'username' => 'sophia',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'user_type' => 'escort',
                'is_active' => true,
            ],
            [
                'username' => 'emma',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'user_type' => 'escort',
                'is_active' => true,
            ],
            [
                'username' => 'olivia',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'user_type' => 'escort',
                'is_active' => true,
            ],
            [
                'username' => 'ava',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'user_type' => 'escort',
                'is_active' => true,
            ],
            [
                'username' => 'isabella',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'user_type' => 'escort',
                'is_active' => true,
            ],
        ];

        $userIds = [];
        foreach ($users as $user) {
            $userId = DB::table('users')->insertGetId([
                'username' => $user['username'],
                'email' => $user['email'],
                'password' => $user['password'],
                'user_type' => $user['user_type'],
                'is_active' => $user['is_active'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            $userIds[] = $userId;
        }

        // Now create escorts linked to these users
        $escorts = [
            [
                'user_id' => $userIds[0],
                'name' => 'Sophia',
                'gender' => 'female',
                'date_of_birth' => '1998-05-15',
                'ethnicity' => 'Caucasian',
                'hair_color' => 'Blonde',
                'hair_length' => 'Long',
                'bust_size' => 'Large(C)',
                'height_cm' => 170,
                'weight_kg' => 55,
                'build' => 'Slim',
                'looks' => 'Ultra Sexy',
                'smoker' => false,
                'about' => 'I am a sophisticated companion with a passion for fine dining and intellectual conversation.',
                'sports' => 'Yoga, Swimming',
                'hobbies' => 'Reading, Traveling',
                'zodiac_sign' => 'Taurus',
                'sexual_orientation' => 'Bisexual',
                'occupation' => 'Model',
                'is_premium' => true,
                'is_verified' => true,
                'is_featured' => true,
                'is_independent' => true,
                'incall_available' => true,
                'outcall_available' => true,
                'is_new' => true,
                'slug' => 'sophia',
            ],
            [
                'user_id' => $userIds[1],
                'name' => 'Emma',
                'gender' => 'female',
                'date_of_birth' => '2000-03-22',
                'ethnicity' => 'Latin',
                'hair_color' => 'Brown',
                'hair_length' => 'Shoulder',
                'bust_size' => 'Medium(B)',
                'height_cm' => 165,
                'weight_kg' => 52,
                'build' => 'Slim',
                'looks' => 'Sexy',
                'smoker' => false,
                'about' => 'Vibrant and energetic companion who loves dancing and adventure.',
                'sports' => 'Dancing, Running',
                'hobbies' => 'Music, Cooking',
                'zodiac_sign' => 'Aries',
                'sexual_orientation' => 'Straight',
                'occupation' => 'Dancer',
                'is_premium' => false,
                'is_verified' => true,
                'is_featured' => false,
                'is_independent' => true,
                'incall_available' => true,
                'outcall_available' => true,
                'is_new' => true,
                'slug' => 'emma',
            ],
            [
                'user_id' => $userIds[2],
                'name' => 'Olivia',
                'gender' => 'female',
                'date_of_birth' => '1996-11-10',
                'ethnicity' => 'Caucasian',
                'hair_color' => 'Red',
                'hair_length' => 'Long',
                'bust_size' => 'Very Large(D)',
                'height_cm' => 175,
                'weight_kg' => 58,
                'build' => 'Regular',
                'looks' => 'Ultra Sexy',
                'smoker' => true,
                'about' => 'Elegant and sophisticated with a passion for art and culture.',
                'sports' => 'Pilates',
                'hobbies' => 'Art, Theater',
                'zodiac_sign' => 'Scorpio',
                'sexual_orientation' => 'Bisexual',
                'occupation' => 'Artist',
                'is_premium' => true,
                'is_verified' => true,
                'is_featured' => true,
                'is_independent' => true,
                'incall_available' => false,
                'outcall_available' => true,
                'is_new' => false,
                'slug' => 'olivia',
            ],
            [
                'user_id' => $userIds[3],
                'name' => 'Ava',
                'gender' => 'female',
                'date_of_birth' => '2001-07-05',
                'ethnicity' => 'Asian',
                'hair_color' => 'Black',
                'hair_length' => 'Long',
                'bust_size' => 'Medium(B)',
                'height_cm' => 160,
                'weight_kg' => 50,
                'build' => 'Skinny',
                'looks' => 'Sexy',
                'smoker' => false,
                'about' => 'Sweet and gentle companion with a love for traditional culture.',
                'sports' => 'Yoga',
                'hobbies' => 'Calligraphy, Tea Ceremony',
                'zodiac_sign' => 'Cancer',
                'sexual_orientation' => 'Straight',
                'occupation' => 'Student',
                'is_premium' => false,
                'is_verified' => true,
                'is_featured' => false,
                'is_independent' => true,
                'incall_available' => true,
                'outcall_available' => false,
                'is_new' => true,
                'slug' => 'ava',
            ],
            [
                'user_id' => $userIds[4],
                'name' => 'Isabella',
                'gender' => 'female',
                'date_of_birth' => '1997-09-18',
                'ethnicity' => 'White',
                'hair_color' => 'Blonde',
                'hair_length' => 'Shoulder',
                'bust_size' => 'Medium(B)',
                'height_cm' => 168,
                'weight_kg' => 54,
                'build' => 'Regular',
                'looks' => 'Sexy',
                'smoker' => false,
                'about' => 'Adventurous spirit with a love for outdoor activities and travel.',
                'sports' => 'Hiking, Climbing',
                'hobbies' => 'Photography, Travel',
                'zodiac_sign' => 'Virgo',
                'sexual_orientation' => 'Bisexual',
                'occupation' => 'Travel Guide',
                'is_premium' => true,
                'is_verified' => true,
                'is_featured' => true,
                'is_independent' => true,
                'incall_available' => false,
                'outcall_available' => true,
                'is_new' => false,
                'slug' => 'isabella',
            ],
        ];

        foreach ($escorts as $escort) {
            DB::table('escorts')->insert([
                'user_id' => $escort['user_id'],
                'name' => $escort['name'],
                'gender' => $escort['gender'],
                'date_of_birth' => $escort['date_of_birth'],
                'ethnicity' => $escort['ethnicity'],
                'hair_color' => $escort['hair_color'],
                'hair_length' => $escort['hair_length'],
                'bust_size' => $escort['bust_size'],
                'height_cm' => $escort['height_cm'],
                'weight_kg' => $escort['weight_kg'],
                'build' => $escort['build'],
                'looks' => $escort['looks'],
                'smoker' => $escort['smoker'],
                'about' => $escort['about'],
                'sports' => $escort['sports'],
                'hobbies' => $escort['hobbies'],
                'zodiac_sign' => $escort['zodiac_sign'],
                'sexual_orientation' => $escort['sexual_orientation'],
                'occupation' => $escort['occupation'],
                'is_premium' => $escort['is_premium'],
                'is_verified' => $escort['is_verified'],
                'is_featured' => $escort['is_featured'],
                'is_independent' => $escort['is_independent'],
                'incall_available' => $escort['incall_available'],
                'outcall_available' => $escort['outcall_available'],
                'is_new' => $escort['is_new'],
                'slug' => $escort['slug'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
