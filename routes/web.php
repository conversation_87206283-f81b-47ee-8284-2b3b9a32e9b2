<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\EscortsController;
use App\Http\Controllers\AgencyController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Home route - now points to escorts
Route::get('/', [EscortsController::class, 'index'])->name('home');

// Escort routes
Route::get('/escorts', [EscortsController::class, 'index'])->name('escorts.index');
Route::get('/escorts/search', [EscortsController::class, 'search'])->name('escorts.search');
Route::get('/escorts/{slug}', [EscortsController::class, 'show'])->name('escorts.show');

// Agency routes
Route::get('/agencies', [AgencyController::class, 'index'])->name('agencies.index');
Route::get('/agencies/search', [AgencyController::class, 'search'])->name('agencies.search');
Route::get('/agencies/{slug}', [AgencyController::class, 'show'])->name('agencies.show')->where('slug', '[A-Za-z0-9\-]+');

// Location routes
Route::get('/locations', [App\Http\Controllers\LocationsController::class, 'index'])->name('locations.index');
Route::get('/locations/{slug}', [App\Http\Controllers\LocationsController::class, 'show'])->name('locations.show');


// City Guide routes
Route::get('/city-guides', [App\Http\Controllers\CityGuideController::class, 'index'])->name('city-guides.index');
Route::post('/admin/city-guides/generate', [App\Http\Controllers\CityGuideController::class, 'generateAll'])->name('city-guides.generate');

// Content Generation routes (Admin only)
Route::prefix('admin/content')->middleware(['auth', 'admin'])->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\ContentGenerationController::class, 'dashboard'])->name('admin.content.dashboard');
    Route::post('/generate/city-guides', [App\Http\Controllers\ContentGenerationController::class, 'generateCityGuides'])->name('admin.content.city-guides');
    Route::post('/generate/safety-pages', [App\Http\Controllers\ContentGenerationController::class, 'generateSafetyPages'])->name('admin.content.safety-pages');
    Route::post('/generate/service-pages', [App\Http\Controllers\ContentGenerationController::class, 'generateServicePages'])->name('admin.content.service-pages');
    Route::post('/generate/all', [App\Http\Controllers\ContentGenerationController::class, 'generateAllContent'])->name('admin.content.generate-all');
    Route::get('/competitive-analysis', [App\Http\Controllers\ContentGenerationController::class, 'getCompetitiveAnalysis'])->name('admin.content.competitive-analysis');
    Route::get('/progress', [App\Http\Controllers\ContentGenerationController::class, 'getProgress'])->name('admin.content.progress');
    Route::get('/export-plan', [App\Http\Controllers\ContentGenerationController::class, 'exportContentPlan'])->name('admin.content.export-plan');
});
Route::get('/api/locations', [App\Http\Controllers\LocationsController::class, 'getLocations'])->name('api.locations');

// Main Sitemap routes
Route::get('/sitemap.xml', [App\Http\Controllers\SitemapController::class, 'index'])->name('sitemap.index');
Route::get('/sitemap-main.xml', [App\Http\Controllers\SitemapController::class, 'main'])->name('sitemap.main');
Route::get('/sitemap-escorts.xml', [App\Http\Controllers\SitemapController::class, 'escorts'])->name('sitemap.escorts');
Route::get('/sitemap-agencies.xml', [App\Http\Controllers\SitemapController::class, 'agencies'])->name('sitemap.agencies');

// Location Sitemap routes
Route::get('/sitemap-locations.xml', [App\Http\Controllers\LocationSitemapController::class, 'index'])->name('sitemap.locations');
Route::get('/sitemap-locations-index.xml', [App\Http\Controllers\LocationSitemapController::class, 'sitemapIndex'])->name('sitemap.locations.index');
Route::get('/sitemap-locations-{countrySlug}.xml', [App\Http\Controllers\LocationSitemapController::class, 'countryLocationSitemap'])->name('sitemap.locations.country');

// Contact routes - HIDDEN: Contact links now redirect to WhatsApp
// Route::get('/contact', [App\Http\Controllers\ContactController::class, 'index'])->name('contact.index');
// Route::post('/contact', [App\Http\Controllers\ContactController::class, 'store'])->name('contact.store');

// Terms and Legal routes
Route::get('/terms', [App\Http\Controllers\TermsController::class, 'index'])->name('terms.index');
Route::get('/privacy', [App\Http\Controllers\TermsController::class, 'privacy'])->name('terms.privacy');
Route::get('/terms/pdf', [App\Http\Controllers\TermsController::class, 'viewPdf'])->name('terms.pdf');
Route::get('/terms/download', [App\Http\Controllers\TermsController::class, 'downloadPdf'])->name('terms.download');

// Static Pages routes
Route::get('/about', [App\Http\Controllers\StaticPageController::class, 'about'])->name('about.index');
Route::get('/safety', [App\Http\Controllers\StaticPageController::class, 'safety'])->name('safety.index');
Route::get('/help', [App\Http\Controllers\StaticPageController::class, 'help'])->name('help.index');
Route::get('/verification', [App\Http\Controllers\StaticPageController::class, 'verification'])->name('verification.process');
Route::get('/booking-tips', [App\Http\Controllers\StaticPageController::class, 'bookingTips'])->name('booking.tips');
Route::get('/services', [App\Http\Controllers\StaticPageController::class, 'services'])->name('services.index');
// Route::get('/blog', [App\Http\Controllers\StaticPageController::class, 'blog'])->name('blog.index'); // Removed as not needed
Route::get('/guides', [App\Http\Controllers\StaticPageController::class, 'cityGuides'])->name('guides.index');

// Additional SEO routes (from remote)
Route::get('/sitemap/static.xml', [App\Http\Controllers\SitemapController::class, 'static'])->name('sitemap.static');
Route::get('/sitemap/pages.xml', [App\Http\Controllers\SitemapController::class, 'pages'])->name('sitemap.pages');
Route::get('/sitemap/images.xml', [App\Http\Controllers\SitemapController::class, 'images'])->name('sitemap.images');
Route::get('/robots.txt', [App\Http\Controllers\RobotsController::class, 'index'])->name('robots');

// Performance and PWA routes
Route::get('/sw.js', [App\Http\Controllers\ServiceWorkerController::class, 'serviceWorker'])->name('service-worker');
Route::get('/manifest.json', [App\Http\Controllers\ServiceWorkerController::class, 'manifest'])->name('manifest');
Route::get('/offline', [App\Http\Controllers\ServiceWorkerController::class, 'offline'])->name('offline');

Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth'])
    ->name('dashboard');

// Test route for confirmation modal
Route::get('/test-modal', function() {
    return view('test-modal');
})->middleware(['auth'])->name('test.modal');

Route::middleware('auth')->group(function () {
    // Common profile routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Password change routes
    Route::get('/password/change', [ProfileController::class, 'changePassword'])->name('password.change');
    Route::post('/password/change', [ProfileController::class, 'updatePassword'])->name('password.update');

    // Notification routes
    Route::get('/notifications', [App\Http\Controllers\NotificationController::class, 'index'])->name('notifications.index');
    Route::put('/notifications/read-all', [App\Http\Controllers\NotificationController::class, 'markAllAsRead'])->name('notifications.read-all');
    Route::put('/notifications/{id}/read', [App\Http\Controllers\NotificationController::class, 'markAsRead'])->name('notifications.read');
    Route::delete('/notifications/all', [App\Http\Controllers\NotificationController::class, 'destroyAll'])->name('notifications.destroy-all');
    Route::delete('/notifications/{id}', [App\Http\Controllers\NotificationController::class, 'destroy'])->name('notifications.destroy');

    // Escort profile routes
    Route::middleware('escort')->group(function () {
        Route::get('/profile/escort', [App\Http\Controllers\Escort\ProfileController::class, 'edit'])->name('profile.escort.edit');
        Route::patch('/profile/escort', [App\Http\Controllers\Escort\ProfileController::class, 'update'])->name('profile.escort.update');

        Route::get('/escort/photos', [App\Http\Controllers\Escort\PhotoController::class, 'index'])->name('escort.photos');
        Route::post('/escort/photos', [App\Http\Controllers\Escort\PhotoController::class, 'store'])->name('escort.photos.store');
        Route::delete('/escort/photos/{id}', [App\Http\Controllers\Escort\PhotoController::class, 'destroy'])->name('escort.photos.destroy');
        Route::post('/escort/photos/{id}/set-main', [App\Http\Controllers\Escort\PhotoController::class, 'setMain'])->name('escort.photos.set-main');

        Route::get('/escort/videos', [App\Http\Controllers\Escort\VideoController::class, 'index'])->name('escort.videos');
        Route::post('/escort/videos', [App\Http\Controllers\Escort\VideoController::class, 'store'])->name('escort.videos.store');
        Route::delete('/escort/videos/{id}', [App\Http\Controllers\Escort\VideoController::class, 'destroy'])->name('escort.videos.destroy');

        Route::get('/escort/services', [App\Http\Controllers\Escort\ServiceController::class, 'index'])->name('escort.services');
        Route::post('/escort/services', [App\Http\Controllers\Escort\ServiceController::class, 'update'])->name('escort.services.update');

        Route::get('/escort/rates', [App\Http\Controllers\Escort\RateController::class, 'index'])->name('escort.rates');
        Route::post('/escort/rates', [App\Http\Controllers\Escort\RateController::class, 'store'])->name('escort.rates.store');
        Route::get('/escort/rates/{id}/edit', [App\Http\Controllers\Escort\RateController::class, 'edit'])->name('escort.rates.edit');
        Route::patch('/escort/rates/{id}', [App\Http\Controllers\Escort\RateController::class, 'update'])->name('escort.rates.update');
        Route::delete('/escort/rates/{id}', [App\Http\Controllers\Escort\RateController::class, 'destroy'])->name('escort.rates.destroy');

        Route::get('/escort/locations', [App\Http\Controllers\Escort\LocationController::class, 'index'])->name('escort.locations');
        Route::post('/escort/locations', [App\Http\Controllers\Escort\LocationController::class, 'update'])->name('escort.locations.update');




        // Status request routes
        Route::get('/escort/status-requests', [App\Http\Controllers\Escort\StatusRequestController::class, 'index'])->name('escort.status-requests.index');
        Route::get('/escort/status-requests/create', [App\Http\Controllers\Escort\StatusRequestController::class, 'create'])->name('escort.status-requests.create');
        Route::post('/escort/status-requests', [App\Http\Controllers\Escort\StatusRequestController::class, 'store'])->name('escort.status-requests.store');
        Route::get('/escort/status-requests/{id}/confirm', [App\Http\Controllers\Escort\StatusRequestController::class, 'confirm'])->name('escort.status-requests.confirm');
        Route::get('/escort/status-requests/{id}', [App\Http\Controllers\Escort\StatusRequestController::class, 'show'])->name('escort.status-requests.show');
        Route::delete('/escort/status-requests/{id}', [App\Http\Controllers\Escort\StatusRequestController::class, 'cancel'])->name('escort.status-requests.cancel');
    });

    // Agency profile routes
    Route::middleware('agency')->group(function () {
        Route::get('/profile/agency', [App\Http\Controllers\Agency\ProfileController::class, 'edit'])->name('profile.agency.edit');
        Route::patch('/profile/agency', [App\Http\Controllers\Agency\ProfileController::class, 'update'])->name('profile.agency.update');

        Route::get('/agency/escorts', [App\Http\Controllers\Agency\EscortController::class, 'index'])->name('agency.escorts');
        Route::get('/agency/escorts/create', [App\Http\Controllers\Agency\EscortController::class, 'create'])->name('agency.escorts.create');
        Route::post('/agency/escorts', [App\Http\Controllers\Agency\EscortController::class, 'store'])->name('agency.escorts.store');
        Route::get('/agency/escorts/{id}/edit', [App\Http\Controllers\Agency\EscortController::class, 'edit'])->name('agency.escorts.edit');
        Route::patch('/agency/escorts/{id}', [App\Http\Controllers\Agency\EscortController::class, 'update'])->name('agency.escorts.update');
        Route::delete('/agency/escorts/{id}', [App\Http\Controllers\Agency\EscortController::class, 'destroy'])->name('agency.escorts.destroy');

        Route::get('/agency/locations', [App\Http\Controllers\Agency\LocationController::class, 'index'])->name('agency.locations');
        Route::post('/agency/locations', [App\Http\Controllers\Agency\LocationController::class, 'update'])->name('agency.locations.update');



        // Status request routes
        Route::get('/agency/status-requests', [App\Http\Controllers\Agency\StatusRequestController::class, 'index'])->name('agency.status-requests.index');
        Route::get('/agency/status-requests/create', [App\Http\Controllers\Agency\StatusRequestController::class, 'create'])->name('agency.status-requests.create');
        Route::post('/agency/status-requests', [App\Http\Controllers\Agency\StatusRequestController::class, 'store'])->name('agency.status-requests.store');
        Route::get('/agency/status-requests/{id}/confirm', [App\Http\Controllers\Agency\StatusRequestController::class, 'confirm'])->name('agency.status-requests.confirm');
        Route::get('/agency/status-requests/{id}', [App\Http\Controllers\Agency\StatusRequestController::class, 'show'])->name('agency.status-requests.show');
        Route::delete('/agency/status-requests/{id}', [App\Http\Controllers\Agency\StatusRequestController::class, 'cancel'])->name('agency.status-requests.cancel');
    });



    // Admin routes
    Route::middleware('admin')->prefix('admin')->group(function () {
        Route::get('/users', [App\Http\Controllers\Admin\UserController::class, 'index'])->name('admin.users');
        Route::get('/users/create', [App\Http\Controllers\Admin\UserController::class, 'create'])->name('admin.users.create');
        Route::post('/users', [App\Http\Controllers\Admin\UserController::class, 'store'])->name('admin.users.store');
        Route::get('/users/{id}/edit', [App\Http\Controllers\Admin\UserController::class, 'edit'])->name('admin.users.edit');
        Route::patch('/users/{id}', [App\Http\Controllers\Admin\UserController::class, 'update'])->name('admin.users.update');
        Route::delete('/users/{id}', [App\Http\Controllers\Admin\UserController::class, 'destroy'])->name('admin.users.destroy');

        Route::get('/escorts', [App\Http\Controllers\Admin\EscortController::class, 'index'])->name('admin.escorts');
        Route::get('/escorts/create', [App\Http\Controllers\Admin\EscortController::class, 'create'])->name('admin.escorts.create');
        Route::post('/escorts', [App\Http\Controllers\Admin\EscortController::class, 'store'])->name('admin.escorts.store');
        Route::get('/escorts/{id}/edit', [App\Http\Controllers\Admin\EscortController::class, 'edit'])->name('admin.escorts.edit');
        Route::patch('/escorts/{id}', [App\Http\Controllers\Admin\EscortController::class, 'update'])->name('admin.escorts.update');
        Route::delete('/escorts/{id}', [App\Http\Controllers\Admin\EscortController::class, 'destroy'])->name('admin.escorts.destroy');

        Route::get('/agencies', [App\Http\Controllers\Admin\AgencyController::class, 'index'])->name('admin.agencies');
        Route::get('/agencies/create', [App\Http\Controllers\Admin\AgencyController::class, 'create'])->name('admin.agencies.create');
        Route::post('/agencies', [App\Http\Controllers\Admin\AgencyController::class, 'store'])->name('admin.agencies.store');
        Route::get('/agencies/{id}/edit', [App\Http\Controllers\Admin\AgencyController::class, 'edit'])->name('admin.agencies.edit');
        Route::patch('/agencies/{id}', [App\Http\Controllers\Admin\AgencyController::class, 'update'])->name('admin.agencies.update');
        Route::delete('/agencies/{id}', [App\Http\Controllers\Admin\AgencyController::class, 'destroy'])->name('admin.agencies.destroy');



        Route::get('/locations', [App\Http\Controllers\Admin\LocationController::class, 'index'])->name('admin.locations');
        Route::get('/locations/create', [App\Http\Controllers\Admin\LocationController::class, 'create'])->name('admin.locations.create');
        Route::post('/locations', [App\Http\Controllers\Admin\LocationController::class, 'store'])->name('admin.locations.store');
        Route::get('/locations/{id}/edit', [App\Http\Controllers\Admin\LocationController::class, 'edit'])->name('admin.locations.edit');
        Route::patch('/locations/{id}', [App\Http\Controllers\Admin\LocationController::class, 'update'])->name('admin.locations.update');
        Route::delete('/locations/{id}', [App\Http\Controllers\Admin\LocationController::class, 'destroy'])->name('admin.locations.destroy');

        Route::get('/services', [App\Http\Controllers\Admin\ServiceController::class, 'index'])->name('admin.services');
        Route::get('/services/create', [App\Http\Controllers\Admin\ServiceController::class, 'create'])->name('admin.services.create');
        Route::post('/services', [App\Http\Controllers\Admin\ServiceController::class, 'store'])->name('admin.services.store');
        Route::get('/services/{id}/edit', [App\Http\Controllers\Admin\ServiceController::class, 'edit'])->name('admin.services.edit');
        Route::patch('/services/{id}', [App\Http\Controllers\Admin\ServiceController::class, 'update'])->name('admin.services.update');
        Route::delete('/services/{id}', [App\Http\Controllers\Admin\ServiceController::class, 'destroy'])->name('admin.services.destroy');



        // Announcement routes
        Route::get('/announcements/create', [App\Http\Controllers\Admin\AnnouncementController::class, 'create'])->name('admin.announcements.create');
        Route::post('/announcements', [App\Http\Controllers\Admin\AnnouncementController::class, 'store'])->name('admin.announcements.store');

        // Settings routes
        Route::get('/settings', [App\Http\Controllers\Admin\SettingController::class, 'index'])->name('admin.settings');
        Route::patch('/settings', [App\Http\Controllers\Admin\SettingController::class, 'update'])->name('admin.settings.update');

        // All requests (unified view)
        Route::get('/all-requests', [App\Http\Controllers\Admin\AllRequestsController::class, 'index'])->name('admin.all-requests.index');

        // Status request routes
        Route::get('/status-requests', [App\Http\Controllers\Admin\StatusRequestController::class, 'index'])->name('admin.status-requests.index');
        Route::get('/status-requests/{id}', [App\Http\Controllers\Admin\StatusRequestController::class, 'show'])->name('admin.status-requests.show');
        Route::post('/status-requests/{id}/approve', [App\Http\Controllers\Admin\StatusRequestController::class, 'approve'])->name('admin.status-requests.approve');
        Route::post('/status-requests/{id}/reject', [App\Http\Controllers\Admin\StatusRequestController::class, 'reject'])->name('admin.status-requests.reject');

        // Agency status request routes
        Route::get('/agency-status-requests', [App\Http\Controllers\Admin\AgencyStatusRequestController::class, 'index'])->name('admin.agency-status-requests.index');
        Route::get('/agency-status-requests/{id}', [App\Http\Controllers\Admin\AgencyStatusRequestController::class, 'show'])->name('admin.agency-status-requests.show');
        Route::post('/agency-status-requests/{id}/approve', [App\Http\Controllers\Admin\AgencyStatusRequestController::class, 'approve'])->name('admin.agency-status-requests.approve');
        Route::post('/agency-status-requests/{id}/reject', [App\Http\Controllers\Admin\AgencyStatusRequestController::class, 'reject'])->name('admin.agency-status-requests.reject');

        // Status pricing routes
        Route::get('/status-pricing', [App\Http\Controllers\Admin\StatusPricingController::class, 'index'])->name('admin.status-pricing.index');
        Route::get('/status-pricing/create', [App\Http\Controllers\Admin\StatusPricingController::class, 'create'])->name('admin.status-pricing.create');
        Route::post('/status-pricing', [App\Http\Controllers\Admin\StatusPricingController::class, 'store'])->name('admin.status-pricing.store');
        Route::get('/status-pricing/{id}/edit', [App\Http\Controllers\Admin\StatusPricingController::class, 'edit'])->name('admin.status-pricing.edit');
        Route::patch('/status-pricing/{id}', [App\Http\Controllers\Admin\StatusPricingController::class, 'update'])->name('admin.status-pricing.update');
        Route::delete('/status-pricing/{id}', [App\Http\Controllers\Admin\StatusPricingController::class, 'destroy'])->name('admin.status-pricing.destroy');

        // SEO Management routes
        Route::get('/seo', [App\Http\Controllers\Admin\SeoController::class, 'dashboard'])->name('admin.seo.dashboard');
        Route::get('/seo/sitemaps', [App\Http\Controllers\Admin\SeoController::class, 'sitemaps'])->name('admin.seo.sitemaps');
        Route::get('/seo/analytics', [App\Http\Controllers\Admin\SeoController::class, 'analytics'])->name('admin.seo.analytics');
        Route::get('/seo/performance', [App\Http\Controllers\Admin\SeoController::class, 'performance'])->name('admin.seo.performance');
        Route::get('/seo/keywords', [App\Http\Controllers\Admin\SeoController::class, 'keywords'])->name('admin.seo.keywords');
        Route::post('/seo/regenerate-sitemaps', [App\Http\Controllers\Admin\SeoController::class, 'regenerateSitemaps'])->name('admin.seo.regenerate-sitemaps');
        Route::post('/seo/clear-caches', [App\Http\Controllers\Admin\SeoController::class, 'clearCaches'])->name('admin.seo.clear-caches');

        // Analytics Configuration routes
        Route::get('/analytics/config', [App\Http\Controllers\Admin\AnalyticsConfigController::class, 'index'])->name('admin.analytics.config');
        Route::post('/analytics/config', [App\Http\Controllers\Admin\AnalyticsConfigController::class, 'update'])->name('admin.analytics.config.update');
        Route::post('/analytics/test', [App\Http\Controllers\Admin\AnalyticsConfigController::class, 'test'])->name('admin.analytics.config.test');
        Route::post('/analytics/verify', [App\Http\Controllers\Admin\AnalyticsConfigController::class, 'generateVerification'])->name('admin.analytics.config.verify');
    });
});

require __DIR__.'/auth.php';
